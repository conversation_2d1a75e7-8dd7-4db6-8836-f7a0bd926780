{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/WorldInfoOptimizer/utils.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAIH,sBAAsB;AAEtB;;GAEG;AACH,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,IAAS,EAAU,EAAE;IAC9C,IAAI,OAAO,IAAI,KAAK,QAAQ;QAAE,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;IAClD,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC1C,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC;IACvB,OAAO,GAAG,CAAC,SAAS,CAAC;AACvB,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,CAAC,IAAS,EAAU,EAAE;IAC1D,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;IACjC,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACxC,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,IAAY,EAAU,EAAE;IACnD,IAAI,OAAO,IAAI,KAAK,QAAQ;QAAE,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;IAClD,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IAChD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACtB,OAAO,IAAI,CAAC,KAAK,CAAC;AACpB,CAAC,CAAC;AAEF,mBAAmB;AAEnB;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,IAAY,EAAE,UAAkB,EAAU,EAAE;IACxE,IAAI,CAAC,IAAI;QAAE,OAAO,EAAE,CAAC;IAErB,mBAAmB;IACnB,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,qCAAqC,EAAE,EAAE,CAAC,CAAC;IAE1E,gCAAgC;IAChC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC;QACvB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,sDAAsD;IACtD,MAAM,iBAAiB,GAAG,UAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;IAE5E,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,IAAI,iBAAiB,GAAG,EAAE,IAAI,CAAC,CAAC;QACzD,4DAA4D;QAC5D,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAErC,6DAA6D;QAC7D,OAAO,KAAK;aACT,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACnB,IAAI,CAAC,IAAI;gBAAE,OAAO,EAAE,CAAC;YACrB,uDAAuD;YACvD,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;gBACpB,OAAO,+BAA+B,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC;YAClE,CAAC;iBAAM,CAAC;gBACN,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC;aACD,IAAI,CAAC,EAAE,CAAC,CAAC;IACd,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,CAAC,IAAI,CAAC,yDAAyD,iBAAiB,GAAG,EAAE,CAAC,CAAC,CAAC;QAC/F,+CAA+C;QAC/C,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC;IAC/B,CAAC;AACH,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,OAAO,GAAG,CAAC,IAAY,EAAE,UAAkB,EAAW,EAAE;IACnE,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;QAAE,OAAO,KAAK,CAAC;IACrC,IAAI,CAAC,IAAI;QAAE,OAAO,KAAK,CAAC;IAExB,IAAI,CAAC;QACH,0BAA0B;QAC1B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;YAC1D,OAAO,IAAI,CAAC;QACd,CAAC;QAED,sBAAsB;QACtB,kBAAkB;QAClB,MAAM,iBAAiB,GAAG,UAAU,CAAC,OAAO,CAAC,sBAAsB,EAAE,MAAM,CAAC,CAAC;QAC7E,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;QACjD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,CAAC,IAAI,CAAC,iEAAiE,UAAU,GAAG,EAAE,CAAC,CAAC,CAAC;QAChG,+BAA+B;QAC/B,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC;IAC/D,CAAC;AACH,CAAC,CAAC;AAEF,gBAAgB;AAEhB;;GAEG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,IAAc,EAAE,KAAa,EAAE,EAAE;IACxD,IAAI,OAAe,CAAC;IACpB,OAAO,CAAC,GAAG,IAAW,EAAE,EAAE;QACxB,YAAY,CAAC,OAAO,CAAC,CAAC;QACtB,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;IACnD,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,IAAc,EAAE,KAAa,EAAE,EAAE;IACxD,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,OAAO,CAAC,GAAG,IAAW,EAAE,EAAE;QACxB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,GAAG,GAAG,QAAQ,IAAI,KAAK,EAAE,CAAC;YAC5B,QAAQ,GAAG,GAAG,CAAC;YACf,OAAO,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACvB,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,iBAAiB;AAEjB;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,CAC1B,EAAK,EACL,OAAO,GAAG,oBAAoB,EACL,EAAE;IAC3B,OAAO,CAAC,KAAK,EAAE,GAAG,IAAW,EAAE,EAAE;QAC/B,IAAI,CAAC;YACH,OAAO,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,UAAU,EAAE,KAAK,CAAC,CAAC;gBAC5C,+BAA+B;gBAC/B,kBAAkB;gBAClB,oBAAoB;gBACpB,mBAAmB;gBACnB,mBAAmB;gBACnB,6CAA6C;gBAC7C,MAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC,CAA4B,CAAC;AAChC,CAAC,CAAC;AAEF,kBAAkB;AAElB;;GAEG;AACH,MAAM,CAAC,MAAM,SAAS,GAAG,CAAI,GAAM,EAAK,EAAE;IACxC,IAAI,GAAG,KAAK,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ;QAAE,OAAO,GAAG,CAAC;IACxD,IAAI,GAAG,YAAY,IAAI;QAAE,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAiB,CAAC;IACxE,IAAI,GAAG,YAAY,KAAK;QAAE,OAAO,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAiB,CAAC;IAClF,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,MAAM,SAAS,GAAG,EAAO,CAAC;QAC1B,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;YACtB,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC5B,SAAS,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,OAAO,GAAG,CAAC,GAAQ,EAAW,EAAE;IAC3C,IAAI,GAAG,IAAI,IAAI;QAAE,OAAO,IAAI,CAAC;IAC7B,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,OAAO,GAAG,KAAK,QAAQ;QAAE,OAAO,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC;IAC3E,IAAI,GAAG,YAAY,GAAG,IAAI,GAAG,YAAY,GAAG;QAAE,OAAO,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC;IACpE,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;AACvC,CAAC,CAAC;AAEF,gBAAgB;AAEhB;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,IAAY,EAAU,EAAE;IACnD,OAAO,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;AACvE,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,IAAY,EAAE,SAAiB,EAAU,EAAE;IACtE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,SAAS;QAAE,OAAO,IAAI,CAAC;IACnD,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;AAClD,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,GAAW,EAAU,EAAE;IAChD,IAAI,CAAC,GAAG;QAAE,OAAO,GAAG,CAAC;IACrB,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACpD,CAAC,CAAC;AAEF,eAAe;AAEf;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,GAAW,EAAU,EAAE;IAClD,OAAO,GAAG,CAAC,cAAc,EAAE,CAAC;AAC9B,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,KAAK,GAAG,CAAC,KAAa,EAAE,GAAW,EAAE,GAAW,EAAU,EAAE;IACvE,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AAC7C,CAAC,CAAC;AAEF,eAAe;AAEf;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,SAAiB,EAAU,EAAE;IAC3D,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;IACjC,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;AAC/B,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,SAAiB,EAAU,EAAE;IAC3D,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACvB,MAAM,IAAI,GAAG,GAAG,GAAG,SAAS,CAAC;IAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;IACxC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;IACzC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;IACvC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;IAEpC,IAAI,IAAI,GAAG,CAAC;QAAE,OAAO,GAAG,IAAI,IAAI,CAAC;IACjC,IAAI,KAAK,GAAG,CAAC;QAAE,OAAO,GAAG,KAAK,KAAK,CAAC;IACpC,IAAI,OAAO,GAAG,CAAC;QAAE,OAAO,GAAG,OAAO,KAAK,CAAC;IACxC,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,iBAAiB;AAEjB;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,KAAU,EAAmB,EAAE;IAC3D,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;AAC9D,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,KAAU,EAAmB,EAAE;IAC3D,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC;AACvE,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,KAAU,EAAkB,EAAE;IACzD,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAClD,CAAC,CAAC"}