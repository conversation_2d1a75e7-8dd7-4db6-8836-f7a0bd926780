{"version": 3, "file": "state.js", "sourceRoot": "", "sources": ["../../src/WorldInfoOptimizer/state.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAGH,OAAO,EAAE,sBAAsB,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAElE,iBAAiB;AACjB,MAAM,CAAC,MAAM,QAAQ,GAAa;IAChC,OAAO,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE;IACtC,SAAS,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE;IAC5B,YAAY,EAAE,IAAI;IAClB,YAAY,EAAE,EAAE;IAChB,eAAe,EAAE,IAAI,GAAG,EAAE;IAC1B,aAAa,EAAE,IAAI,GAAG,EAAE;IACxB,SAAS,EAAE,WAAW;IACtB,YAAY,EAAE,KAAK;IACnB,aAAa,EAAE,EAAE,GAAG,sBAAsB,EAAE;IAC5C,eAAe,EAAE,KAAK;IACtB,aAAa,EAAE,IAAI,GAAG,EAAE;CACzB,CAAC;AAEF,mCAAmC;AAEnC;;GAEG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,CAAC,QAAgB,EAAmB,EAAE;IAC1E,IAAI,CAAC;QACH,IAAI,CAAC,QAAQ,CAAC,eAAe,IAAI,CAAC,CAAC,QAAQ,CAAC,eAAe,YAAY,GAAG,CAAC,EAAE,CAAC;YAC5E,OAAO,CAAC,IAAI,CAAC,+EAA+E,CAAC,CAAC;YAC9F,QAAQ,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACvC,CAAC;QAED,IAAI,OAAO,QAAQ,CAAC,eAAe,CAAC,GAAG,KAAK,UAAU,EAAE,CAAC;YACvD,OAAO,CAAC,IAAI,CAAC,wFAAwF,CAAC,CAAC;YACvG,QAAQ,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACvC,CAAC;QAED,MAAM,OAAO,GAAG,QAAQ,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACvD,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IAC/C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uDAAuD,EAAE,KAAK,CAAC,CAAC;QAC9E,QAAQ,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACrC,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,CAAC,QAAgB,EAAE,OAAwB,EAAQ,EAAE;IACzF,IAAI,CAAC;QACH,IAAI,CAAC,QAAQ,CAAC,eAAe,IAAI,CAAC,CAAC,QAAQ,CAAC,eAAe,YAAY,GAAG,CAAC,EAAE,CAAC;YAC5E,OAAO,CAAC,IAAI,CAAC,+EAA+E,CAAC,CAAC;YAC9F,QAAQ,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACvC,CAAC;QAED,IAAI,OAAO,QAAQ,CAAC,eAAe,CAAC,GAAG,KAAK,UAAU,EAAE,CAAC;YACvD,OAAO,CAAC,IAAI,CAAC,wFAAwF,CAAC,CAAC;YACvG,QAAQ,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACvC,CAAC;QAED,QAAQ,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAChF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uDAAuD,EAAE,KAAK,CAAC,CAAC;QAC9E,QAAQ,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACrC,QAAQ,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAChF,CAAC;AACH,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,yBAAyB,GAAG,CAAC,QAAgB,EAAQ,EAAE;IAClE,IAAI,CAAC;QACH,IAAI,CAAC,QAAQ,CAAC,eAAe,IAAI,CAAC,CAAC,QAAQ,CAAC,eAAe,YAAY,GAAG,CAAC,EAAE,CAAC;YAC5E,OAAO,CAAC,IAAI,CAAC,+EAA+E,CAAC,CAAC;YAC9F,QAAQ,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;YACrC,OAAO;QACT,CAAC;QAED,IAAI,OAAO,QAAQ,CAAC,eAAe,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;YAC1D,OAAO,CAAC,IAAI,CAAC,2FAA2F,CAAC,CAAC;YAC1G,QAAQ,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;YACrC,OAAO;QACT,CAAC;QAED,QAAQ,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,0DAA0D,EAAE,KAAK,CAAC,CAAC;QACjF,QAAQ,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;IACvC,CAAC;AACH,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG,GAAS,EAAE;IACjD,IAAI,CAAC;QACH,IAAI,CAAC,QAAQ,CAAC,eAAe,IAAI,CAAC,CAAC,QAAQ,CAAC,eAAe,YAAY,GAAG,CAAC,EAAE,CAAC;YAC5E,OAAO,CAAC,IAAI,CAAC,+EAA+E,CAAC,CAAC;YAC9F,QAAQ,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;YACrC,OAAO;QACT,CAAC;QAED,IAAI,OAAO,QAAQ,CAAC,eAAe,CAAC,KAAK,KAAK,UAAU,EAAE,CAAC;YACzD,OAAO,CAAC,IAAI,CAAC,0FAA0F,CAAC,CAAC;YACzG,QAAQ,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;YACrC,OAAO;QACT,CAAC;QAED,QAAQ,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;IACnC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,yDAAyD,EAAE,KAAK,CAAC,CAAC;QAChF,QAAQ,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;IACvC,CAAC;AACH,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,CAAC,QAAgB,EAAW,EAAE;IAClE,IAAI,CAAC;QACH,IAAI,CAAC,QAAQ,CAAC,eAAe,IAAI,CAAC,CAAC,QAAQ,CAAC,eAAe,YAAY,GAAG,CAAC,EAAE,CAAC;YAC5E,OAAO,CAAC,IAAI,CAAC,+EAA+E,CAAC,CAAC;YAC9F,QAAQ,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,OAAO,QAAQ,CAAC,eAAe,CAAC,GAAG,KAAK,UAAU,EAAE,CAAC;YACvD,OAAO,CAAC,IAAI,CAAC,wFAAwF,CAAC,CAAC;YACvG,QAAQ,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,QAAQ,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAChD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uDAAuD,EAAE,KAAK,CAAC,CAAC;QAC9E,QAAQ,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACrC,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC,CAAC;AAEF,iBAAiB;AAEjB;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,GAAS,EAAE;IACtC,QAAQ,CAAC,OAAO,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;IACjD,QAAQ,CAAC,SAAS,GAAG,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;IACvC,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC;IAC7B,QAAQ,CAAC,YAAY,GAAG,EAAE,CAAC;IAC3B,QAAQ,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;IACrC,QAAQ,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;IACnC,QAAQ,CAAC,SAAS,GAAG,WAAW,CAAC;IACjC,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAC;IAC9B,QAAQ,CAAC,aAAa,GAAG,EAAE,GAAG,sBAAsB,EAAE,CAAC;IACvD,QAAQ,CAAC,eAAe,GAAG,KAAK,CAAC;IACjC,QAAQ,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;AACrC,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,GAAS,EAAE;IAC3C,QAAQ,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AACjC,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAAG,GAAS,EAAE;IAC9C,QAAQ,CAAC,eAAe,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC;IACrD,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;QAC9B,kBAAkB,EAAE,CAAC;IACvB,CAAC;AACH,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,GAAW,EAAQ,EAAE;IAChD,QAAQ,CAAC,SAAS,GAAG,GAAG,CAAC;IACzB,eAAe;IACf,kBAAkB,EAAE,CAAC;AACvB,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,MAAe,EAAQ,EAAE;IACrD,QAAQ,CAAC,YAAY,GAAG,MAAM,CAAC;AACjC,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,CAAC,OAA2C,EAAQ,EAAE;IACvF,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;AACjD,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,OAAe,EAAQ,EAAE;IACvD,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AACtC,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,CAAC,OAAe,EAAQ,EAAE;IAC1D,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACzC,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,CAAC,OAAe,EAAQ,EAAE;IAC1D,IAAI,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;QACxC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;SAAM,CAAC;QACN,eAAe,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC;AACH,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,OAAe,EAAW,EAAE;IACzD,OAAO,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAC7C,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAAG,GAAW,EAAE;IAChD,OAAO,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC;AACrC,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,GAAa,EAAE;IAC7C,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC5C,CAAC,CAAC"}