/**
 * 世界书优化器事件处理模块
 * 包含所有事件处理函数和事件绑定逻辑
 */

import { TavernAPI, loadAllData } from './api';
import { COLLAPSE_ALL_BTN_ID, COLLAPSE_CURRENT_BTN_ID, PANEL_ID, BUTTON_ID, REFRESH_BTN_ID, SEARCH_INPUT_ID } from './constants';
import {
  appState,
  clearSelectedItems,
  safeGetLorebookEntries,
  safeSetLorebookEntries,
  toggleSelectedItem,
  toggleMultiSelectMode,
  setActiveTab
} from './state';
import type { LorebookEntry } from './types';
import { renderContent, showModal, showPanel, hidePanel, showProgressToast, showSuccessTick, updateSelectionCount } from './ui';
import { errorCatched, debounce } from './utils';

// --- 全局变量 ---
let parentWin: any;
let $: any;

/**
 * 设置全局依赖
 */
export const setGlobalDependencies = (jquery: any, parentWindow: any) => {
  $ = jquery;
  parentWin = parentWindow;
};

// --- 核心事件处理函数 ---

/**
 * 处理头部点击事件（展开/折叠）
 */
export const handleHeaderClick = errorCatched(async (event: any) => {
  const $target = $(event.target);
  const $container = $(event.currentTarget).closest('.wio-item-container, .wio-book-group');

  // 如果点击的是按钮等可交互控件，则不执行后续逻辑
  if ($target.closest('.wio-item-controls, .wio-rename-ui').length > 0) {
    return;
  }

  // 多选模式下的选择逻辑
  if (appState.multiSelectMode) {
    let itemKey: string | null = null;

    // 处理世界书组选择
    if ($container.hasClass('wio-book-group')) {
      const bookName = $container.data('book-name');
      itemKey = `book:${bookName}`;
    }
    // 处理条目选择
    else if ($container.hasClass('wio-item-container')) {
      const itemType = $container.data('type');
      const itemId = $container.data('id');
      if (itemType === 'lore') {
        const bookName = $container.data('book-name');
        itemKey = `lore:${bookName}:${itemId}`;
      } else if (itemType === 'regex') {
        itemKey = `regex:${itemId}`;
      }
    }

    if (itemKey) {
      toggleSelectedItem(itemKey);
      $container.toggleClass('selected');
      updateSelectionCount();
    }
    return;
  }

  // 正常模式下的展开/折叠逻辑
  const $content = $container.find('.wio-collapsible-content').first();
  const isExpanded = $content.is(':visible');

  if (isExpanded) {
    $content.slideUp(200);
    $container.addClass('collapsed');
  } else {
    // 展开当前项目并加载内容
    const type = $container.data('type');
    const id = $container.data('id');
    let item: any, editorHtml: string;

    if (type === 'lore') {
      const bookName = $container.data('book-name');
      const entries = [...safeGetLorebookEntries(bookName)];
      item = entries.find((e: any) => e.uid === id);
      if (!item) return;

      editorHtml = createLorebookEntryEditor(item);
    } else if (type === 'regex') {
      const allRegexes = [...appState.regexes.global, ...appState.regexes.character];
      item = allRegexes.find((r: any) => r.id === id);
      if (!item) return;

      editorHtml = createRegexEditor(item);
    } else {
      // 世界书组展开，显示条目列表
      $content.slideDown(200);
      $container.removeClass('collapsed');
      return;
    }

    $content.html(editorHtml);
    $content.slideDown(200);
    $container.removeClass('collapsed');
  }
});

/**
 * 处理切换状态事件（启用/禁用）
 */
export const handleToggleState = errorCatched(async (event: any) => {
  event.stopPropagation();
  const $button = $(event.currentTarget);
  const $elementToSort = $button.closest('.wio-book-group, .wio-item-container');
  if ($elementToSort.hasClass('renaming')) return;

  const isEnabling = !$elementToSort.hasClass('enabled');

  // 更新UI状态
  if (isEnabling) {
    $elementToSort.removeClass('disabled').addClass('enabled');
    $button.html('<i class="fa-solid fa-toggle-on"></i>');
  } else {
    $elementToSort.removeClass('enabled').addClass('disabled');
    $button.html('<i class="fa-solid fa-toggle-off"></i>');
  }

  // 处理世界书启用/禁用
  if ($elementToSort.hasClass('wio-book-group')) {
    const bookName = $elementToSort.data('book-name');
    const currentSettings = await TavernAPI.getLorebookSettings();
    const selectedBooks = new Set(currentSettings?.selected_global_lorebooks || []);

    if (isEnabling) {
      selectedBooks.add(bookName);
    } else {
      selectedBooks.delete(bookName);
    }

    await TavernAPI.setLorebookSettings({
      ...currentSettings,
      selected_global_lorebooks: Array.from(selectedBooks),
    });

    // 更新本地状态
    const bookState = appState.allLorebooks.find(b => b.name === bookName);
    if (bookState) bookState.enabled = isEnabling;
  } else {
    // 处理条目或正则启用/禁用
    const type = $elementToSort.data('type');
    const id = $elementToSort.data('id');

    if (type === 'lore') {
      const bookName = $elementToSort.data('book-name');
      await TavernAPI.setLorebookEntries(bookName, [{ uid: Number(id), enabled: isEnabling }]);
      const entry = safeGetLorebookEntries(bookName).find((e: any) => e.uid === Number(id));
      if (entry) entry.enabled = isEnabling;
    } else {
      // 处理正则启用/禁用
      const allServerRegexes = await TavernAPI.getRegexes();
      const regex = allServerRegexes.find((r: any) => r.id === id);
      if (regex) {
        regex.enabled = isEnabling;
        await TavernAPI.replaceRegexes(allServerRegexes.filter((r: any) => r.source !== 'card'));
        await TavernAPI.saveSettings();
      }
    }
  }

  showSuccessTick('操作成功');
});

/**
 * 处理重命名事件
 */
export const handleRename = errorCatched(async (event: any) => {
  event.stopPropagation();
  const $container = $(event.currentTarget).closest('.wio-item-container');
  if ($container.hasClass('renaming') || $container.length === 0) return;

  const $header = $container.find('.wio-item-header').first();
  const $nameSpan = $header.find('.wio-item-name').first();
  const currentName = $nameSpan.text().trim();

  // 进入重命名模式
  $container.addClass('renaming');
  const renameHtml = `
    <div class="wio-rename-ui">
      <input type="text" class="wio-rename-input" value="${currentName}" />
      <button class="wio-rename-save-btn wio-btn wio-btn-primary">
        <i class="fa-solid fa-check"></i>
      </button>
      <button class="wio-rename-cancel-btn wio-btn wio-btn-secondary">
        <i class="fa-solid fa-times"></i>
      </button>
    </div>
  `;

  $nameSpan.hide();
  $nameSpan.after(renameHtml);

  const $input = $container.find('.wio-rename-input');
  $input.focus().select();
});

/**
 * 退出重命名模式
 */
const exitRenameMode = ($container: any, newName: string | null = null) => {
  const $header = $container.find('.wio-item-header').first();
  const $nameSpan = $header.find('.wio-item-name').first();

  if (newName) {
    $nameSpan.text(newName);
  }

  $nameSpan.show();
  $container.find('.wio-rename-ui').remove();
  $container.removeClass('renaming');
};

/**
 * 确认重命名
 */
export const handleConfirmRename = errorCatched(async (event: any) => {
  event.stopPropagation();
  const $container = $(event.currentTarget).closest('.wio-item-container');
  const $input = $container.find('.wio-rename-input');
  const newName = $input.val().trim();
  const oldName = $container.find('.wio-item-name').first().text().trim();

  if (!newName) {
    $input.addClass('wio-input-error');
    setTimeout(() => $input.removeClass('wio-input-error'), 500);
    return;
  }

  if (newName === oldName) {
    exitRenameMode($container);
    return;
  }

  const type = $container.data('type');
  const id = $container.data('id');

  if (type === 'lore') {
    const bookName = $container.data('book-name');
    await TavernAPI.setLorebookEntries(bookName, [{ uid: Number(id), comment: newName }]);
    const entry = safeGetLorebookEntries(bookName).find((e: any) => e.uid === Number(id));
    if (entry) entry.comment = newName;
  } else {
    // type === 'regex'
    const allServerRegexes = await TavernAPI.getRegexes();
    const regex = allServerRegexes.find((r: any) => r.id === id);
    if (regex) {
      regex.script_name = newName;
      await TavernAPI.replaceRegexes(allServerRegexes.filter((r: any) => r.source !== 'card'));
      await TavernAPI.saveSettings();
    }
  }

  exitRenameMode($container, newName);
  showSuccessTick('操作成功');
});

/**
 * 取消重命名
 */
export const handleCancelRename = errorCatched(async (event: any) => {
  event.stopPropagation();
  const $container = $(event.currentTarget).closest('.wio-item-container');
  exitRenameMode($container);
});

/**
 * 处理重命名输入框按键事件
 */
export const handleRenameKeydown = errorCatched(async (event: any) => {
  if (event.key === 'Enter') {
    $(event.currentTarget).siblings('.wio-rename-save-btn').click();
  } else if (event.key === 'Escape') {
    $(event.currentTarget).siblings('.wio-rename-cancel-btn').click();
  }
});

// --- 辅助函数 ---

/**
 * 创建世界书条目编辑器
 */
const createLorebookEntryEditor = (entry: LorebookEntry): string => {
  return `
    <div class="wio-editor-wrapper">
      <div class="wio-editor-field">
        <label>条目名称:</label>
        <input type="text" class="wio-editor-input" data-field="comment" value="${entry.comment || ''}" />
      </div>
      <div class="wio-editor-field">
        <label>关键词 (用逗号分隔):</label>
        <input type="text" class="wio-editor-input" data-field="keys" value="${(entry.keys || []).join(', ')}" />
      </div>
      <div class="wio-editor-field">
        <label>内容:</label>
        <textarea class="wio-editor-textarea" data-field="content" rows="6">${entry.content || ''}</textarea>
      </div>
      <div class="wio-editor-actions">
        <button class="wio-btn wio-btn-primary wio-save-editor-btn">
          <i class="fa-solid fa-save"></i> 保存
        </button>
        <button class="wio-btn wio-btn-secondary wio-cancel-editor-btn">
          <i class="fa-solid fa-times"></i> 取消
        </button>
      </div>
    </div>
  `;
};

/**
 * 创建正则表达式编辑器
 */
const createRegexEditor = (regex: any): string => {
  return `
    <div class="wio-editor-wrapper">
      <div class="wio-editor-field">
        <label>正则名称:</label>
        <input type="text" class="wio-editor-input" data-field="script_name" value="${regex.script_name || ''}" />
      </div>
      <div class="wio-editor-field">
        <label>查找正则:</label>
        <input type="text" class="wio-editor-input" data-field="find_regex" value="${regex.find_regex || ''}" />
      </div>
      <div class="wio-editor-field">
        <label>替换字符串:</label>
        <textarea class="wio-editor-textarea" data-field="replace_string" rows="3">${regex.replace_string || ''}</textarea>
      </div>
      <div class="wio-editor-actions">
        <button class="wio-btn wio-btn-primary wio-save-editor-btn">
          <i class="fa-solid fa-save"></i> 保存
        </button>
        <button class="wio-btn wio-btn-secondary wio-cancel-editor-btn">
          <i class="fa-solid fa-times"></i> 取消
        </button>
      </div>
    </div>
  `;
};

// --- 删除操作事件处理 ---

/**
 * 处理删除条目事件
 */
export const handleDeleteEntry = errorCatched(async (event: any) => {
  event.stopPropagation();
  const $item = $(event.currentTarget).closest('.wio-item-container');
  const bookName = $item.data('book-name');
  const uid = Number($item.data('id'));
  const entryName = $item.find('.wio-item-name').text().trim();

  const confirmed = await showModal({
    type: 'confirm',
    title: '确认删除',
    text: `确定要删除条目 "${entryName}" 吗？此操作不可撤销。`,
  });

  if (confirmed) {
    await TavernAPI.deleteLorebookEntries(bookName, [uid.toString()]);

    // 更新本地状态
    const entries = safeGetLorebookEntries(bookName);
    const updatedEntries = entries.filter(e => e.uid !== uid.toString());
    safeSetLorebookEntries(bookName, updatedEntries);

    $item.fadeOut(300, () => $item.remove());
    showSuccessTick('操作成功');
  }
});

/**
 * 处理删除世界书事件
 */
export const handleDeleteBook = errorCatched(async (event: any) => {
  event.stopPropagation();
  const $bookGroup = $(event.currentTarget).closest('.wio-book-group');
  const bookName = $bookGroup.data('book-name');

  const confirmed = await showModal({
    type: 'confirm',
    title: '确认删除',
    text: `确定要删除世界书 "${bookName}" 吗？这将删除其中的所有条目，此操作不可撤销。`,
  });

  if (confirmed) {
    const progressToast = showProgressToast('正在处理...');

    try {
      await TavernAPI.deleteLorebook(bookName);

      // 更新本地状态
      appState.allLorebooks = appState.allLorebooks.filter(b => b.name !== bookName);

      $bookGroup.fadeOut(300, () => $bookGroup.remove());
      progressToast.remove();
      showSuccessTick('操作成功');
    } catch (error) {
      progressToast.remove();
      await showModal({
        type: 'alert',
          title: '删除失败',
          text: `删除世界书时发生错误: ${typeof error === 'object' && error !== null && 'message' in error ? error.message : String(error)}`
      });
    }
  }
});

// --- 批量操作事件处理 ---

/**
 * 处理全选事件
 */
export const handleSelectAll = errorCatched(async () => {
  const parentDoc = parentWin.document;
  const $visibleItems = $(`#${PANEL_ID} .wio-item-container:visible, #${PANEL_ID} .wio-book-group:visible`, parentDoc);

  $visibleItems.each((_: number, element: HTMLElement) => {
    const $item = $(element);
    const itemKey = getItemKey($item);
    if (itemKey && canSelectItem($item)) {
      appState.selectedItems.add(itemKey);
      $item.addClass('selected');
    }
  });

  updateSelectionCount();
  showSuccessTick('操作成功');
});

/**
 * 处理取消全选事件
 */
export const handleDeselectAll = errorCatched(async () => {
  const parentDoc = parentWin.document;
  clearSelectedItems();
  $(`#${PANEL_ID} .selected`, parentDoc).removeClass('selected');
  updateSelectionCount();
  showSuccessTick('操作成功');
});

/**
 * 处理反选事件
 */
export const handleInvertSelection = errorCatched(async () => {
  const parentDoc = parentWin.document;
  const $visibleItems = $(`#${PANEL_ID} .wio-item-container:visible, #${PANEL_ID} .wio-book-group:visible`, parentDoc);

  $visibleItems.each((_: number, element: HTMLElement) => {
    const $item = $(element);
    const itemKey = getItemKey($item);
    if (itemKey && canSelectItem($item)) {
      if (appState.selectedItems.has(itemKey)) {
        appState.selectedItems.delete(itemKey);
        $item.removeClass('selected');
      } else {
        appState.selectedItems.add(itemKey);
        $item.addClass('selected');
      }
    }
  });

  updateSelectionCount();
  showSuccessTick('操作成功');
});

/**
 * 获取项目键值
 */
const getItemKey = ($item: any): string | null => {
  const isGlobalLoreTab = appState.activeTab === 'global-lore';
  const isBookHeader = $item.hasClass('wio-book-group');

  if (isBookHeader && isGlobalLoreTab) {
    const bookName = $item.data('book-name');
    return `book:${bookName}`;
  } else if ($item.hasClass('wio-item-container')) {
    const itemType = $item.data('type');
    const itemId = $item.data('id');
    if (itemType === 'lore') {
      const bookName = $item.data('book-name');
      return `lore:${bookName}:${itemId}`;
    } else if (itemType === 'regex') {
      return `regex:${itemId}`;
    }
  }
  return null;
};

/**
 * 检查项目是否可选择
 */
const canSelectItem = ($item: any): boolean => {
  const isGlobalLoreTab = appState.activeTab === 'global-lore';
  const isBookHeader = $item.hasClass('wio-book-group');

  // 世界书头部只能在全局世界书标签页选择
  if (isBookHeader) {
    return isGlobalLoreTab;
  }

  // 其他项目都可以选择
  return true;
};

// --- 折叠操作事件处理 ---

/**
 * 处理折叠所有事件
 */
export const handleCollapseAll = errorCatched(async () => {
  const parentDoc = parentWin.document;
  const $allCollapsible = $(`#${PANEL_ID} .wio-collapsible-content`, parentDoc);

  $allCollapsible.slideUp(200);
  $allCollapsible.closest('.wio-item-container, .wio-book-group').addClass('collapsed');
  showSuccessTick('操作成功');
});

/**
 * 处理折叠当前标签页事件
 */
export const handleCollapseCurrent = errorCatched(async () => {
  const parentDoc = parentWin.document;
  const $currentTabContent = $(`#${PANEL_ID}-content .wio-collapsible-content`, parentDoc);

  $currentTabContent.slideUp(200);
  $currentTabContent.closest('.wio-item-container, .wio-book-group').addClass('collapsed');
  showSuccessTick('操作成功');
});

// --- 编辑器事件处理 ---

/**
 * 处理保存编辑器事件
 */
export const handleSaveEditor = errorCatched(async (event: any) => {
  event.stopPropagation();
  const $editor = $(event.currentTarget).closest('.wio-editor-wrapper');
  const $container = $editor.closest('.wio-item-container');
  const type = $container.data('type');
  const id = $container.data('id');

  if (type === 'lore') {
    const bookName = $container.data('book-name');
    const entries = [...safeGetLorebookEntries(bookName)];
    const entry = entries.find((e: any) => e.uid === id);
    if (!entry) return;

    // 收集编辑器中的数据
    const updatedData: any = { uid: Number(id) };
    $editor.find('.wio-editor-input, .wio-editor-textarea').each((_: number, element: HTMLElement) => {
      const $field = $(element);
      const fieldName = $field.data('field');
      let value = $field.val();

      if (fieldName === 'keys') {
        // 处理关键词字段
        value = value
          ? value
              .split(',')
              .map((k: string) => k.trim())
              .filter((k: string) => k)
          : [];
      }

      updatedData[fieldName] = value;
    });

    await TavernAPI.setLorebookEntries(bookName, [updatedData]);

    // 更新本地状态
    Object.assign(entry, updatedData);

    // 更新UI中的名称显示
    if (updatedData.comment) {
      $container.find('.wio-item-name').first().text(updatedData.comment);
    }
  } else if (type === 'regex') {
    const allServerRegexes = await TavernAPI.getRegexes();
    const regex = allServerRegexes.find((r: any) => r.id === id);
    if (!regex) return;

    // 收集编辑器中的数据
    $editor.find('.wio-editor-input, .wio-editor-textarea').each((_: number, element: HTMLElement) => {
      const $field = $(element);
      const fieldName = $field.data('field');
      const value = $field.val();
      regex[fieldName] = value;
    });

    await TavernAPI.replaceRegexes(allServerRegexes.filter((r: any) => r.source !== 'card'));
    await TavernAPI.saveSettings();

    // 更新UI中的名称显示
    if (regex.script_name) {
      $container.find('.wio-item-name').first().text(regex.script_name);
    }
  }

  // 关闭编辑器
  $container.find('.wio-collapsible-content').slideUp(200);
  $container.addClass('collapsed');
  showSuccessTick('操作成功');
});

/**
 * 处理取消编辑器事件
 */
export const handleCancelEditor = errorCatched(async (event: any) => {
  event.stopPropagation();
  const $container = $(event.currentTarget).closest('.wio-item-container');

  // 关闭编辑器
  $container.find('.wio-collapsible-content').slideUp(200);
  $container.addClass('collapsed');
});

// --- 主要事件绑定函数 ---

/**
 * 绑定所有事件处理器
 */
export const bindEventHandlers = (): void => {
  const parentDoc = parentWin.document;

  // 扩展菜单按钮点击事件
  $(parentDoc).on('click', `#${BUTTON_ID}`, async () => {
    const $panel = $(`#${PANEL_ID}`, parentDoc);
    if ($panel.is(':visible')) {
      hidePanel();
    } else {
      await showPanel(() => loadAllData(renderContent));
    }
  });

  // 面板关闭按钮
  $(parentDoc).on('click', '#wio-close-btn', () => {
    hidePanel();
  });

  // 搜索输入框事件
  const debouncedSearch = debounce(() => {
    renderContent();
  }, 300);

  $(parentDoc).on('input', `#${SEARCH_INPUT_ID}`, debouncedSearch);
  $(parentDoc).on('click', '#wio-clear-search-btn', () => {
    $(`#${SEARCH_INPUT_ID}`, parentDoc).val('').trigger('input');
  });

  // 刷新按钮
  $(parentDoc).on('click', `#${REFRESH_BTN_ID}`, async () => {
    await loadAllData(renderContent);
  });

  // 标签页切换
  $(parentDoc).on('click', '.wio-tab-btn', (event: any) => {
    const $tab = $(event.currentTarget);
    const tabName = $tab.data('tab');

    $('.wio-tab-btn', parentDoc).removeClass('active');
    $tab.addClass('active');

    setActiveTab(tabName);
    renderContent();
  });

  // 多选模式切换
  $(parentDoc).on('click', '.wio-multi-select-toggle', () => {
    toggleMultiSelectMode();
    renderContent();
  });

  // 折叠操作
  $(parentDoc).on('click', `#${COLLAPSE_ALL_BTN_ID}`, handleCollapseAll);
  $(parentDoc).on('click', `#${COLLAPSE_CURRENT_BTN_ID}`, handleCollapseCurrent);

  // 批量操作
  $(parentDoc).on('click', '#wio-select-all-btn', handleSelectAll);
  $(parentDoc).on('click', '#wio-deselect-all-btn', handleDeselectAll);
  $(parentDoc).on('click', '#wio-invert-selection-btn', handleInvertSelection);

  // 项目头部点击（展开/折叠/选择）
  $(parentDoc).on('click', '.wio-item-header', handleHeaderClick);

  // 项目控制按钮
  $(parentDoc).on('click', '.wio-toggle-btn', handleToggleState);
  $(parentDoc).on('click', '.wio-rename-btn', handleRename);
  $(parentDoc).on('click', '.wio-delete-btn', (event: any) => {
    const $container = $(event.currentTarget).closest('.wio-item-container, .wio-book-group');
    if ($container.hasClass('wio-book-group')) {
      handleDeleteBook(event);
    } else {
      handleDeleteEntry(event);
    }
  });

  // 重命名相关事件
  $(parentDoc).on('click', '.wio-rename-save-btn', handleConfirmRename);
  $(parentDoc).on('click', '.wio-rename-cancel-btn', handleCancelRename);
  $(parentDoc).on('keydown', '.wio-rename-input', handleRenameKeydown);

  // 编辑器相关事件
  $(parentDoc).on('click', '.wio-save-editor-btn', handleSaveEditor);
  $(parentDoc).on('click', '.wio-cancel-editor-btn', handleCancelEditor);

  console.log('[WorldInfoOptimizer] Event handlers bound successfully.');
};
