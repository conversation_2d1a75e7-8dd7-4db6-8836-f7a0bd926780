{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/WorldInfoOptimizer/index.ts"], "names": [], "mappings": "AAAA,iBAAiB;AACjB,8CAA8C;AAC9C,sDAAsD;AACtD,oBAAoB;AACpB,sBAAsB;AACtB,oEAAoE;AACpE,wCAAwC;AACxC,qBAAqB;AACrB,wBAAwB;AACxB,kBAAkB;AAElB,YAAY,CAAC;AAEb,kBAAkB;AAClB,CAAC,GAAG,EAAE;IACJ,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;IAE9D,eAAe;IACf,+BAA+B;IAC/B,iBAAiB;IAEjB,SAAS;IACT,2EAA2E;IAE3E,OAAO;IACP,+DAA+D;IAE/D,SAAS;IACT,qDAAqD;IAErD,SAAS;IACT,0CAA0C;IAE1C,UAAU;IACV,yGAAyG;IAEzG,SAAS;IACT,WAAW;IACX,gDAAgD;IAChD,qBAAqB;IACrB,2BAA2B;IAC3B,mBAAmB;IACnB,mBAAmB;IACnB,iBAAiB;IAEjB,WAAW;IACX,gGAAgG;IAEhG,eAAe;IACf,IAAI,SAAc,CAAC;IACnB,IAAI,CAAM,CAAC;IACX,yDAAyD;IACzD,IAAI,YAAiB,CAAC;IAEtB;;OAEG;IACH,SAAS,OAAO,CAAC,QAAkD;QACjE,MAAM,WAAW,GAAG,iBAAiB,CAAC;QACtC,MAAM,UAAU,GAAG,GAAG,CAAC;QACvB,IAAI,OAAO,GAAG,CAAC,CAAC;QAEhB,OAAO,CAAC,GAAG,CACT,2EAA2E,WAAW,kBAAkB,CACzG,CAAC;QAEF,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE;YAChC,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YACzC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC;YAE1B,MAAM,QAAQ,GAAG,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC;YAC/D,MAAM,QAAQ,GACZ,SAAS,CAAC,YAAY,IAAI,OAAO,SAAS,CAAC,YAAY,CAAC,WAAW,KAAK,UAAU,IAAI,SAAS,CAAC,MAAM,CAAC;YAEzG,IAAI,QAAQ,IAAI,QAAQ,EAAE,CAAC;gBACzB,aAAa,CAAC,QAAQ,CAAC,CAAC;gBACxB,OAAO,CAAC,GAAG,CAAC,sFAAsF,CAAC,CAAC;gBACpG,IAAI,CAAC;oBACH,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC;gBACrD,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,OAAO,CAAC,KAAK,CAAC,mEAAmE,EAAE,CAAC,CAAC,CAAC;gBACxF,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,EAAE,CAAC;gBACV,IAAI,OAAO,GAAG,UAAU,EAAE,CAAC;oBACzB,aAAa,CAAC,QAAQ,CAAC,CAAC;oBACxB,OAAO,CAAC,KAAK,CAAC,wDAAwD,CAAC,CAAC;oBACxE,IAAI,CAAC,QAAQ;wBAAE,OAAO,CAAC,KAAK,CAAC,iDAAiD,WAAW,cAAc,CAAC,CAAC;oBACzG,IAAI,CAAC,QAAQ;wBAAE,OAAO,CAAC,KAAK,CAAC,2DAA2D,CAAC,CAAC;gBAC5F,CAAC;YACH,CAAC;QACH,CAAC,EAAE,GAAG,CAAC,CAAC;IACV,CAAC;IAED,0BAA0B;IAC1B,2BAA2B;IAE3B,cAAc;IACd,MAAM,cAAc,GAAG,GAAS,EAAE;QAChC,MAAM,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC;QAErC,YAAY;QACZ,IAAI,SAAS,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC;YAC3C,OAAO;QACT,CAAC;QAED,MAAM,YAAY,GAAG,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QACtD,YAAY,CAAC,EAAE,GAAG,YAAY,CAAC;QAC/B,YAAY,CAAC,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAkD1B,CAAC;QAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QACzC,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;IAC1D,CAAC,CAAC;IAEF,cAAc;IACd,MAAM,qBAAqB,GAAG,GAAS,EAAE;QACvC,MAAM,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC;QACrC,MAAM,QAAQ,GAAG,6BAA6B,CAAC;QAE/C,YAAY;QACZ,IAAI,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvC,OAAO,CAAC,GAAG,CAAC,0EAA0E,CAAC,CAAC;YACxF,OAAO;QACT,CAAC;QAED,MAAM,UAAU,GAAG;iBACN,QAAQ;;;;KAIpB,CAAC;QAEF,YAAY;QACZ,MAAM,eAAe,GAAG,CAAC,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC;QACxD,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACnC,OAAO,CAAC,GAAG,CAAC,iEAAiE,CAAC,CAAC;QACjF,CAAC;aAAM,CAAC;YACN,oBAAoB;YACpB,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,oCAAoC,UAAU,QAAQ,CAAC,CAAC;YACjF,OAAO,CAAC,GAAG,CAAC,iEAAiE,CAAC,CAAC;QACjF,CAAC;QAED,SAAS;QACT,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,QAAQ,EAAE,EAAE,GAAG,EAAE;YAC5C,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,kBAAkB;IAClB,MAAM,eAAe,GAAG,GAAS,EAAE;QACjC,MAAM,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC;QACrC,MAAM,OAAO,GAAG,4BAA4B,CAAC;QAE7C,YAAY;QACZ,IAAI,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,+DAA+D,CAAC,CAAC;YAC7E,OAAO;QACT,CAAC;QAED,MAAM,SAAS,GAAG;iBACL,OAAO;;;;;;;KAOnB,CAAC;QAEF,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACpC,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;IACvE,CAAC,CAAC;IAEF,oBAAoB;IACpB,SAAS,+BAA+B,CAAC,MAAW,EAAE,YAAiB;QACrE,CAAC,GAAG,MAAM,CAAC;QACX,YAAY,GAAG,YAAY,CAAC;QAC5B,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC;QAE1B,OAAO,CAAC,GAAG,CAAC,oEAAoE,CAAC,CAAC;QAElF,IAAI,CAAC;YACH,SAAS;YACT,cAAc,EAAE,CAAC;YAEjB,SAAS;YACT,qBAAqB,EAAE,CAAC;YACxB,eAAe,EAAE,CAAC;YAElB,OAAO,CAAC,GAAG,CAAC,yEAAyE,CAAC,CAAC;YACvF,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAC/B,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;YACnC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6DAA6D,EAAE,KAAK,CAAC,CAAC;QACtF,CAAC;IACH,CAAC;IAED,gBAAgB;IAChB,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;IAC/D,OAAO,CAAC,+BAA+B,CAAC,CAAC;AAC3C,CAAC,CAAC,EAAE,CAAC"}