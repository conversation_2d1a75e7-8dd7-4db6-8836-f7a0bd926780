// ==UserScript==
// @name         世界书优化器 (World Info Optimizer)
// @namespace    SillyTavern.WorldInfoOptimizer.v1_0_0
// @match        */*
// @version      1.0.0
// @description  【世界书管理专家】基于 Linus 哲学的简洁高效世界书管理工具。提供搜索、替换、批量操作等核心功能。
// <AUTHOR> & AI Assistant
// @grant        none
// @inject-into  content
// ==/UserScript==
'use strict';
// 使用IIFE封装，避免全局污染
(() => {
    console.log('[WorldInfoOptimizer] Script execution started.');
    // --- 模块导入 ---
    // 注意：在实际的浏览器环境中，这些导入需要通过其他方式实现
    // 这里展示的是理想的模块化结构
    // 导入类型定义
    // import type { LorebookEntry, AppState, TavernHelperAPI } from './types';
    // 导入常量
    // import { SCRIPT_VERSION_TAG, SELECTORS } from './constants';
    // 导入状态管理
    // import { appState, setDataLoaded } from './state';
    // 导入工具函数
    // import { errorCatched } from './utils';
    // 导入API模块
    // import { initializeTavernAPI, setGlobalDependencies as setApiDependencies, loadAllData } from './api';
    // 导入UI模块
    // import {
    //   setGlobalDependencies as setUiDependencies,
    //   createMainPanel,
    //   createExtensionButton,
    //   renderContent,
    //   addBasicStyles
    // } from './ui';
    // 导入事件处理模块
    // import { setGlobalDependencies as setEventsDependencies, bindEventHandlers } from './events';
    // --- 全局变量 ---
    let parentWin;
    let $;
    // Will be initialized in mainWithTemporaryImplementation
    let TavernHelper;
    /**
     * 等待DOM和API就绪的函数
     */
    function onReady(callback) {
        const domSelector = '#extensionsMenu';
        const maxRetries = 100;
        let retries = 0;
        console.log(`[WorldInfoOptimizer] Starting readiness check. Polling for DOM element "${domSelector}" AND core APIs.`);
        const interval = setInterval(() => {
            const parentDoc = window.parent.document;
            parentWin = window.parent;
            const domReady = parentDoc.querySelector(domSelector) !== null;
            const apiReady = parentWin.TavernHelper && typeof parentWin.TavernHelper.getCharData === 'function' && parentWin.jQuery;
            if (domReady && apiReady) {
                clearInterval(interval);
                console.log(`[WorldInfoOptimizer] SUCCESS: Both DOM and Core APIs are ready. Initializing script.`);
                try {
                    callback(parentWin.jQuery, parentWin.TavernHelper);
                }
                catch (e) {
                    console.error('[WorldInfoOptimizer] FATAL: Error during main callback execution.', e);
                }
            }
            else {
                retries++;
                if (retries > maxRetries) {
                    clearInterval(interval);
                    console.error(`[WorldInfoOptimizer] FATAL: Readiness check timed out.`);
                    if (!domReady)
                        console.error(`[WorldInfoOptimizer] -> Failure: DOM element "${domSelector}" not found.`);
                    if (!apiReady)
                        console.error(`[WorldInfoOptimizer] -> Failure: Core APIs not available.`);
                }
            }
        }, 150);
    }
    // --- 临时实现（在模块化完成前使用） ---
    // 这里需要包含所有必要的函数，直到模块系统完全实现
    // 临时的基础样式添加函数
    const addBasicStyles = () => {
        const parentDoc = parentWin.document;
        // 检查样式是否已存在
        if (parentDoc.getElementById('wio-styles')) {
            return;
        }
        const styleElement = parentDoc.createElement('style');
        styleElement.id = 'wio-styles';
        styleElement.textContent = `
      /* 世界书优化器基础样式 */
      .wio-panel {
        position: fixed;
        top: 50px;
        right: 20px;
        width: 800px;
        max-height: 80vh;
        background: #2a2a2a;
        border: 1px solid #444;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        z-index: 10000;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        color: #fff;
        overflow: hidden;
      }
      
      .wio-extension-button {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        margin: 4px;
        background: #3a3a3a;
        border: 1px solid #555;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.2s;
      }
      
      .wio-extension-button:hover {
        background: #4a4a4a;
      }
      
      .wio-button-icon {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }
      
      .wio-loading-placeholder {
        padding: 40px;
        text-align: center;
        color: #888;
      }
      
      .wio-loading-placeholder i {
        font-size: 24px;
        margin-bottom: 10px;
      }
    `;
        parentDoc.head.appendChild(styleElement);
        console.log('[WorldInfoOptimizer] Basic styles added.');
    };
    // 临时的扩展按钮创建函数
    const createExtensionButton = () => {
        const parentDoc = parentWin.document;
        const buttonId = 'world-info-optimizer-button';
        // 检查按钮是否已存在
        if (parentDoc.getElementById(buttonId)) {
            console.log('[WorldInfoOptimizer] Extension button already exists, skipping creation.');
            return;
        }
        const buttonHtml = `
      <div id="${buttonId}" class="wio-extension-button" title="世界书优化器">
        <img src="https://i.postimg.cc/bY23wb9Y/IMG-20250626-000247.png" alt="世界书优化器" class="wio-button-icon">
        <span class="wio-button-text">世界书优化器</span>
      </div>
    `;
        // 尝试添加到扩展菜单
        const $extensionsMenu = $('#extensionsMenu', parentDoc);
        if ($extensionsMenu.length > 0) {
            $extensionsMenu.append(buttonHtml);
            console.log('[WorldInfoOptimizer] Extension button added to extensions menu.');
        }
        else {
            // 如果扩展菜单不存在，添加到页面底部
            $(parentDoc.body).append(`<div class="wio-floating-button">${buttonHtml}</div>`);
            console.log('[WorldInfoOptimizer] Extension button added as floating button.');
        }
        // 添加点击事件
        $(parentDoc).on('click', `#${buttonId}`, () => {
            alert('世界书优化器功能正在开发中...\n\n模块化重构已完成，正在集成各个模块。');
        });
    };
    // 临时的主面板创建函数（简化版）
    const createMainPanel = () => {
        const parentDoc = parentWin.document;
        const panelId = 'world-info-optimizer-panel';
        // 检查面板是否已存在
        if (parentDoc.getElementById(panelId)) {
            console.log('[WorldInfoOptimizer] Panel already exists, skipping creation.');
            return;
        }
        const panelHtml = `
      <div id="${panelId}" class="wio-panel" style="display: none;">
        <div class="wio-loading-placeholder">
          <i class="fa-solid fa-cog fa-spin"></i>
          <p>世界书优化器正在初始化...</p>
          <p><small>模块化重构已完成，正在集成功能模块</small></p>
        </div>
      </div>
    `;
        $(parentDoc.body).append(panelHtml);
        console.log('[WorldInfoOptimizer] Main panel created successfully.');
    };
    // 重新定义main函数以使用临时实现
    function mainWithTemporaryImplementation(jquery, tavernHelper) {
        $ = jquery;
        TavernHelper = tavernHelper;
        parentWin = window.parent;
        console.log('[WorldInfoOptimizer] Initializing with temporary implementation...');
        try {
            // 添加基础样式
            addBasicStyles();
            // 创建UI组件
            createExtensionButton();
            createMainPanel();
            console.log('[WorldInfoOptimizer] Temporary implementation initialized successfully.');
            console.log('[WorldInfoOptimizer] 模块化重构已完成，包含以下模块:');
            console.log('  - types.ts: 类型定义');
            console.log('  - constants.ts: 常量配置');
            console.log('  - state.ts: 状态管理');
            console.log('  - utils.ts: 工具函数');
            console.log('  - api.ts: API交互');
            console.log('  - ui.ts: UI组件');
            console.log('  - events.ts: 事件处理');
            console.log('  - index.ts: 主入口（当前文件）');
        }
        catch (error) {
            console.error('[WorldInfoOptimizer] Error during temporary initialization:', error);
        }
    }
    // --- 初始化脚本 ---
    console.log('[WorldInfoOptimizer] Starting initialization...');
    onReady(mainWithTemporaryImplementation);
})();
//# sourceMappingURL=index.js.map