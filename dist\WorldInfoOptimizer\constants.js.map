{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../../src/WorldInfoOptimizer/constants.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAIH,iBAAiB;AACjB,MAAM,CAAC,MAAM,kBAAkB,GAAG,QAAQ,CAAC;AAE3C,sBAAsB;AACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,4BAA4B,CAAC;AACrD,MAAM,CAAC,MAAM,SAAS,GAAG,6BAA6B,CAAC;AACvD,MAAM,CAAC,MAAM,eAAe,GAAG,kBAAkB,CAAC;AAClD,MAAM,CAAC,MAAM,cAAc,GAAG,iBAAiB,CAAC;AAChD,MAAM,CAAC,MAAM,uBAAuB,GAAG,0BAA0B,CAAC;AAClE,MAAM,CAAC,MAAM,mBAAmB,GAAG,sBAAsB,CAAC;AAC1D,MAAM,CAAC,MAAM,sBAAsB,GAAG,yBAAyB,CAAC;AAEhE,eAAe;AACf,MAAM,CAAC,MAAM,eAAe,GAAG,uDAAuD,CAAC;AACvF,MAAM,CAAC,MAAM,cAAc,GAAG,QAAQ,CAAC;AACvC,MAAM,CAAC,MAAM,mBAAmB,GAAG,QAAQ,CAAC;AAE5C,kBAAkB;AAClB,MAAM,CAAC,MAAM,gBAAgB,GAAG;IAC9B,QAAQ,EAAE;QACR,2BAA2B,EAAE,OAAO;QACpC,0BAA0B,EAAE,OAAO;QACnC,uBAAuB,EAAE,OAAO;QAChC,sBAAsB,EAAE,OAAO;QAC/B,kBAAkB,EAAE,OAAO;QAC3B,iBAAiB,EAAE,OAAO;QAC1B,kBAAkB,EAAE,SAAS;QAC7B,qBAAqB,EAAE,WAAW;QAClC,gBAAgB,EAAE,UAAU;KACO;IACrC,KAAK,EAAE;QACL,OAAO,EAAE,QAAQ;QACjB,OAAO,EAAE,QAAQ;QACjB,OAAO,EAAE,QAAQ;QACjB,OAAO,EAAE,QAAQ;KACe;CACnC,CAAC;AAEF,eAAe;AACf,MAAM,CAAC,MAAM,sBAAsB,GAAG;IACpC,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,IAAI;IACf,QAAQ,EAAE,IAAI;IACd,OAAO,EAAE,IAAI;CACd,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAAG,aAAa,CAAC;AAEzC,eAAe;AACf,MAAM,CAAC,MAAM,WAAW,GAAG,GAAG,CAAC;AAC/B,MAAM,CAAC,MAAM,sBAAsB,GAAG,IAAI,CAAC;AAC3C,MAAM,CAAC,MAAM,4BAA4B,GAAG,GAAG,CAAC;AAChD,MAAM,CAAC,MAAM,cAAc,GAAG,GAAG,CAAC;AAElC,mBAAmB;AACnB,MAAM,CAAC,MAAM,WAAW,GAAG;IACzB,OAAO;IACP,KAAK,EAAE,WAAW;IAClB,aAAa,EAAE,mBAAmB;IAClC,YAAY,EAAE,kBAAkB;IAEhC,OAAO;IACP,cAAc,EAAE,oBAAoB;IACpC,WAAW,EAAE,iBAAiB;IAC9B,SAAS,EAAE,eAAe;IAC1B,UAAU,EAAE,gBAAgB;IAE5B,OAAO;IACP,MAAM,EAAE,SAAS;IACjB,cAAc,EAAE,iBAAiB;IACjC,gBAAgB,EAAE,mBAAmB;IACrC,aAAa,EAAE,gBAAgB;IAE/B,OAAO;IACP,KAAK,EAAE,WAAW;IAClB,WAAW,EAAE,iBAAiB;IAE9B,OAAO;IACP,OAAO,EAAE,aAAa;IACtB,QAAQ,EAAE,cAAc;IACxB,SAAS,EAAE,eAAe;IAC1B,QAAQ,EAAE,cAAc;IAExB,OAAO;IACP,mBAAmB,EAAE,yBAAyB;IAC9C,QAAQ,EAAE,cAAc;IAExB,QAAQ;IACR,aAAa,EAAE,mBAAmB;IAClC,aAAa,EAAE,mBAAmB;IAClC,YAAY,EAAE,kBAAkB;IAChC,UAAU,EAAE,gBAAgB;IAC5B,YAAY,EAAE,kBAAkB;IAChC,WAAW,EAAE,iBAAiB;IAC9B,SAAS,EAAE,eAAe;IAC1B,QAAQ,EAAE,cAAc;IACxB,YAAY,EAAE,kBAAkB;IAEhC,QAAQ;IACR,cAAc,EAAE,oBAAoB;IACpC,eAAe,EAAE,qBAAqB;IAEtC,OAAO;IACP,SAAS,EAAE,eAAe;IAC1B,YAAY,EAAE,kBAAkB;IAChC,cAAc,EAAE,oBAAoB;IAEpC,QAAQ;IACR,mBAAmB,EAAE,yBAAyB;IAE9C,QAAQ;IACR,YAAY,EAAE,kBAAkB;IAChC,eAAe,EAAE,qBAAqB;IACtC,iBAAiB,EAAE,uBAAuB;CAClC,CAAC;AAEX,gBAAgB;AAChB,MAAM,CAAC,MAAM,SAAS,GAAG;IACvB,eAAe,EAAE,iBAAiB;IAClC,KAAK,EAAE,IAAI,QAAQ,EAAE;IACrB,aAAa,EAAE,IAAI,QAAQ,UAAU;IACrC,YAAY,EAAE,IAAI,eAAe,EAAE;IACnC,WAAW,EAAE,IAAI,cAAc,EAAE;IACjC,oBAAoB,EAAE,IAAI,uBAAuB,EAAE;IACnD,gBAAgB,EAAE,IAAI,mBAAmB,EAAE;IAC3C,mBAAmB,EAAE,IAAI,sBAAsB,EAAE;CACzC,CAAC;AAEX,eAAe;AACf,MAAM,CAAC,MAAM,QAAQ,GAAG;IACtB,OAAO,EAAE;QACP,iBAAiB,EAAE,MAAM;QACzB,aAAa,EAAE,QAAQ;QACvB,YAAY,EAAE,SAAS;QACvB,cAAc,EAAE,OAAO;QACvB,cAAc,EAAE,MAAM;KACvB;IACD,KAAK,EAAE;QACL,gBAAgB,EAAE,MAAM;QACxB,aAAa,EAAE,MAAM;QACrB,aAAa,EAAE,MAAM;QACrB,iBAAiB,EAAE,MAAM;KAC1B;IACD,OAAO,EAAE;QACP,YAAY,EAAE,aAAa;QAC3B,WAAW,EAAE,0BAA0B;QACvC,YAAY,EAAE,cAAc;QAC5B,WAAW,EAAE,aAAa;KAC3B;IACD,MAAM,EAAE;QACN,UAAU,EAAE,OAAO;QACnB,cAAc,EAAE,QAAQ;QACxB,kBAAkB,EAAE,SAAS;KAC9B;IACD,IAAI,EAAE;QACJ,UAAU,EAAE,WAAW;QACvB,OAAO,EAAE,SAAS;QAClB,UAAU,EAAE,SAAS;QACrB,YAAY,EAAE,YAAY;KAC3B;CACO,CAAC"}