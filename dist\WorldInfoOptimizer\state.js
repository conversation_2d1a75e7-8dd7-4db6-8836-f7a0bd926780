/**
 * 世界书优化器状态管理
 * 包含应用状态定义和所有状态管理相关的纯函数
 */
import { DEFAULT_SEARCH_FILTERS, DEFAULT_TAB } from './constants';
// --- 应用程序状态 ---
export const appState = {
    regexes: { global: [], character: [] },
    lorebooks: { character: [] },
    chatLorebook: null,
    allLorebooks: [],
    lorebookEntries: new Map(),
    lorebookUsage: new Map(),
    activeTab: DEFAULT_TAB,
    isDataLoaded: false,
    searchFilters: { ...DEFAULT_SEARCH_FILTERS },
    multiSelectMode: false,
    selectedItems: new Set(),
};
// --- 安全访问 lorebookEntries 的函数 ---
/**
 * 安全获取世界书条目
 */
export const safeGetLorebookEntries = (bookName) => {
    try {
        if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
            console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
            appState.lorebookEntries = new Map();
        }
        if (typeof appState.lorebookEntries.get !== 'function') {
            console.warn('[WorldInfoOptimizer] appState.lorebookEntries.get is not a function, reinitializing...');
            appState.lorebookEntries = new Map();
        }
        const entries = appState.lorebookEntries.get(bookName);
        return Array.isArray(entries) ? entries : [];
    }
    catch (error) {
        console.error('[WorldInfoOptimizer] Error in safeGetLorebookEntries:', error);
        appState.lorebookEntries = new Map();
        return [];
    }
};
/**
 * 安全设置世界书条目
 */
export const safeSetLorebookEntries = (bookName, entries) => {
    try {
        if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
            console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
            appState.lorebookEntries = new Map();
        }
        if (typeof appState.lorebookEntries.set !== 'function') {
            console.warn('[WorldInfoOptimizer] appState.lorebookEntries.set is not a function, reinitializing...');
            appState.lorebookEntries = new Map();
        }
        appState.lorebookEntries.set(bookName, Array.isArray(entries) ? entries : []);
    }
    catch (error) {
        console.error('[WorldInfoOptimizer] Error in safeSetLorebookEntries:', error);
        appState.lorebookEntries = new Map();
        appState.lorebookEntries.set(bookName, Array.isArray(entries) ? entries : []);
    }
};
/**
 * 安全删除世界书条目
 */
export const safeDeleteLorebookEntries = (bookName) => {
    try {
        if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
            console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
            appState.lorebookEntries = new Map();
            return;
        }
        if (typeof appState.lorebookEntries.delete !== 'function') {
            console.warn('[WorldInfoOptimizer] appState.lorebookEntries.delete is not a function, reinitializing...');
            appState.lorebookEntries = new Map();
            return;
        }
        appState.lorebookEntries.delete(bookName);
    }
    catch (error) {
        console.error('[WorldInfoOptimizer] Error in safeDeleteLorebookEntries:', error);
        appState.lorebookEntries = new Map();
    }
};
/**
 * 安全清空所有世界书条目
 */
export const safeClearLorebookEntries = () => {
    try {
        if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
            console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
            appState.lorebookEntries = new Map();
            return;
        }
        if (typeof appState.lorebookEntries.clear !== 'function') {
            console.warn('[WorldInfoOptimizer] appState.lorebookEntries.clear is not a function, reinitializing...');
            appState.lorebookEntries = new Map();
            return;
        }
        appState.lorebookEntries.clear();
    }
    catch (error) {
        console.error('[WorldInfoOptimizer] Error in safeClearLorebookEntries:', error);
        appState.lorebookEntries = new Map();
    }
};
/**
 * 安全检查是否存在世界书条目
 */
export const safeHasLorebookEntries = (bookName) => {
    try {
        if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
            console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
            appState.lorebookEntries = new Map();
            return false;
        }
        if (typeof appState.lorebookEntries.has !== 'function') {
            console.warn('[WorldInfoOptimizer] appState.lorebookEntries.has is not a function, reinitializing...');
            appState.lorebookEntries = new Map();
            return false;
        }
        return appState.lorebookEntries.has(bookName);
    }
    catch (error) {
        console.error('[WorldInfoOptimizer] Error in safeHasLorebookEntries:', error);
        appState.lorebookEntries = new Map();
        return false;
    }
};
// --- 状态重置函数 ---
/**
 * 重置应用状态到初始值
 */
export const resetAppState = () => {
    appState.regexes = { global: [], character: [] };
    appState.lorebooks = { character: [] };
    appState.chatLorebook = null;
    appState.allLorebooks = [];
    appState.lorebookEntries = new Map();
    appState.lorebookUsage = new Map();
    appState.activeTab = DEFAULT_TAB;
    appState.isDataLoaded = false;
    appState.searchFilters = { ...DEFAULT_SEARCH_FILTERS };
    appState.multiSelectMode = false;
    appState.selectedItems = new Set();
};
/**
 * 清空选中项目
 */
export const clearSelectedItems = () => {
    appState.selectedItems.clear();
};
/**
 * 切换多选模式
 */
export const toggleMultiSelectMode = () => {
    appState.multiSelectMode = !appState.multiSelectMode;
    if (!appState.multiSelectMode) {
        clearSelectedItems();
    }
};
/**
 * 设置活动标签页
 */
export const setActiveTab = (tab) => {
    appState.activeTab = tab;
    // 切换标签页时清空选中项目
    clearSelectedItems();
};
/**
 * 设置数据加载状态
 */
export const setDataLoaded = (loaded) => {
    appState.isDataLoaded = loaded;
};
/**
 * 更新搜索过滤器
 */
export const updateSearchFilters = (filters) => {
    Object.assign(appState.searchFilters, filters);
};
/**
 * 添加选中项目
 */
export const addSelectedItem = (itemKey) => {
    appState.selectedItems.add(itemKey);
};
/**
 * 移除选中项目
 */
export const removeSelectedItem = (itemKey) => {
    appState.selectedItems.delete(itemKey);
};
/**
 * 切换项目选中状态
 */
export const toggleSelectedItem = (itemKey) => {
    if (appState.selectedItems.has(itemKey)) {
        removeSelectedItem(itemKey);
    }
    else {
        addSelectedItem(itemKey);
    }
};
/**
 * 检查项目是否被选中
 */
export const isItemSelected = (itemKey) => {
    return appState.selectedItems.has(itemKey);
};
/**
 * 获取选中项目数量
 */
export const getSelectedItemsCount = () => {
    return appState.selectedItems.size;
};
/**
 * 获取所有选中项目
 */
export const getSelectedItems = () => {
    return Array.from(appState.selectedItems);
};
//# sourceMappingURL=state.js.map