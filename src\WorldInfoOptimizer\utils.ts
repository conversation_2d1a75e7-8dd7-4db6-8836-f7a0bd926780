/**
 * 世界书优化器工具函数
 * 包含所有通用工具函数
 */

import type { ErrorCatchedFunction } from './types';

// --- HTML 处理工具函数 ---

/**
 * HTML 转义函数
 */
export const escapeHtml = (text: any): string => {
  if (typeof text !== 'string') return String(text);
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
};

/**
 * 处理文本内容并保留换行符
 */
export const escapeHtmlWithNewlines = (text: any): string => {
  const escaped = escapeHtml(text);
  return escaped.replace(/\n/g, '<br>');
};

/**
 * HTML 反转义函数
 */
export const unescapeHtml = (html: string): string => {
  if (typeof html !== 'string') return String(html);
  const area = document.createElement('textarea');
  area.innerHTML = html;
  return area.value;
};

// --- 文本处理工具函数 ---

/**
 * 高亮文本中的搜索词
 */
export const highlightText = (text: string, searchTerm: string): string => {
  if (!text) return '';

  // 移除任何现有的高亮标签，防止嵌套
  const cleanText = text.replace(/<\/?mark\s+class="wio-highlight">/gi, '');

  // 如果没有搜索词，只返回清理后的纯文本（不需要HTML转义）
  if (!searchTerm.trim()) {
    return cleanText;
  }

  // Escape special characters in search term for regex.
  const escapedSearchTerm = searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

  try {
    const regex = new RegExp(`(${escapedSearchTerm})`, 'gi');
    // Split the text by the search term, keeping the delimiter.
    const parts = cleanText.split(regex);

    // Process parts: escape normal text, wrap matches in <mark>.
    return parts
      .map((part, index) => {
        if (!part) return '';
        // The matched parts are at odd indices (1, 3, 5, ...).
        if (index % 2 === 1) {
          return `<mark class="wio-highlight">${escapeHtml(part)}</mark>`;
        } else {
          return escapeHtml(part);
        }
      })
      .join('');
  } catch (e) {
    console.warn(`[WorldInfoOptimizer] Invalid regex for highlighting: "${escapedSearchTerm}"`, e);
    // Fallback to just escaping the text on error.
    return escapeHtml(cleanText);
  }
};

/**
 * 检查文本是否匹配搜索词
 */
export const isMatch = (text: string, searchTerm: string): boolean => {
  if (!searchTerm.trim()) return false;
  if (!text) return false;

  try {
    // 首先尝试简单的文本包含检查（对JSON更友好）
    if (text.toLowerCase().includes(searchTerm.toLowerCase())) {
      return true;
    }

    // 然后尝试正则匹配（处理转义后的搜索词）
    // 转义所有正则特殊字符，包括引号
    const escapedSearchTerm = searchTerm.replace(/[.*+?^${}()|[\]\\"]/g, '\\$&');
    const regex = new RegExp(escapedSearchTerm, 'i');
    return regex.test(text);
  } catch (e) {
    console.warn(`[WorldInfoOptimizer] Invalid regex created from search term: "${searchTerm}"`, e);
    // 作为备选方案，如果正则表达式无效，则退回到简单的文本搜索
    return text.toLowerCase().includes(searchTerm.toLowerCase());
  }
};

// --- 函数式工具 ---

/**
 * 防抖函数
 */
export const debounce = (func: Function, delay: number) => {
  let timeout: number;
  return (...args: any[]) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), delay);
  };
};

/**
 * 节流函数
 */
export const throttle = (func: Function, delay: number) => {
  let lastCall = 0;
  return (...args: any[]) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      return func(...args);
    }
  };
};

// --- 错误处理工具 ---

/**
 * 错误处理包装器
 */
export const errorCatched = <T extends (...args: any[]) => any>(
  fn: T,
  context = 'WorldInfoOptimizer',
): ErrorCatchedFunction<T> => {
  return (async (...args: any[]) => {
    try {
      return await fn(...args);
    } catch (error) {
      if (error) {
        console.error(`[${context}] Error:`, error);
        // 注意：这里需要在实际使用时导入 showModal 函数
        // 为了避免循环依赖，这里先注释掉
        // await showModal({
        //   type: 'alert',
        //   title: '脚本异常',
        //   text: `操作失败: ${error.message || error}`,
        // });
      }
    }
  }) as ErrorCatchedFunction<T>;
};

// --- 数组和对象工具 ---

/**
 * 深拷贝对象
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T;
  if (typeof obj === 'object') {
    const clonedObj = {} as T;
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
  return obj;
};

/**
 * 检查对象是否为空
 */
export const isEmpty = (obj: any): boolean => {
  if (obj == null) return true;
  if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;
  if (obj instanceof Map || obj instanceof Set) return obj.size === 0;
  return Object.keys(obj).length === 0;
};

// --- 字符串工具 ---

/**
 * 生成安全的 ID（用于 CSS 选择器）
 */
export const createSafeId = (name: string): string => {
  return 'wio-' + btoa(encodeURIComponent(name)).replace(/[+/=]/g, '');
};

/**
 * 截断文本
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
};

/**
 * 首字母大写
 */
export const capitalize = (str: string): string => {
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1);
};

// --- 数字工具 ---

/**
 * 格式化数字（添加千分位分隔符）
 */
export const formatNumber = (num: number): string => {
  return num.toLocaleString();
};

/**
 * 限制数字在指定范围内
 */
export const clamp = (value: number, min: number, max: number): number => {
  return Math.min(Math.max(value, min), max);
};

// --- 时间工具 ---

/**
 * 格式化时间戳
 */
export const formatTimestamp = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleString();
};

/**
 * 获取相对时间描述
 */
export const getRelativeTime = (timestamp: number): string => {
  const now = Date.now();
  const diff = now - timestamp;
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) return `${days}天前`;
  if (hours > 0) return `${hours}小时前`;
  if (minutes > 0) return `${minutes}分钟前`;
  return '刚刚';
};

// --- 类型检查工具 ---

/**
 * 检查是否为有效的字符串
 */
export const isValidString = (value: any): value is string => {
  return typeof value === 'string' && value.trim().length > 0;
};

/**
 * 检查是否为有效的数字
 */
export const isValidNumber = (value: any): value is number => {
  return typeof value === 'number' && !isNaN(value) && isFinite(value);
};

/**
 * 检查是否为有效的数组
 */
export const isValidArray = (value: any): value is any[] => {
  return Array.isArray(value) && value.length > 0;
};
