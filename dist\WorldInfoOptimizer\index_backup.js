// ==UserScript==
// @name         世界书优化器 (World Info Optimizer)
// @namespace    SillyTavern.WorldInfoOptimizer.v1_0_0
// @match        */*
// @version      1.0.0
// @description  【世界书管理专家】基于 Linus 哲学的简洁高效世界书管理工具。提供搜索、替换、批量操作等核心功能。
// <AUTHOR> & AI Assistant
// @grant        none
// @inject-into  content
// ==/UserScript==
'use strict';
// 使用IIFE封装，避免全局污染
(() => {
    console.log('[WorldInfoOptimizer] Script execution started.');
    // --- 配置常量 ---
    const SCRIPT_VERSION_TAG = 'v1_0_0';
    const PANEL_ID = 'world-info-optimizer-panel';
    const BUTTON_ID = 'world-info-optimizer-button';
    const BUTTON_ICON_URL = 'https://i.postimg.cc/bY23wb9Y/IMG-20250626-000247.png';
    const BUTTON_TOOLTIP = '世界书优化器';
    const BUTTON_TEXT_IN_MENU = '世界书优化器';
    const SEARCH_INPUT_ID = 'wio-search-input';
    const REFRESH_BTN_ID = 'wio-refresh-btn';
    const COLLAPSE_CURRENT_BTN_ID = 'wio-collapse-current-btn';
    const COLLAPSE_ALL_BTN_ID = 'wio-collapse-all-btn';
    const CREATE_LOREBOOK_BTN_ID = 'wio-create-lorebook-btn';
    const LOREBOOK_OPTIONS = {
        position: {
            before_character_definition: '角色定义前',
            after_character_definition: '角色定义后',
            before_example_messages: '聊天示例前',
            after_example_messages: '聊天示例后',
            before_author_note: '作者笔记前',
            after_author_note: '作者笔记后',
            at_depth_as_system: '@D ⚙ 系统',
            at_depth_as_assistant: '@D 🗨️ 角色',
            at_depth_as_user: '@D 👤 用户',
        },
        logic: {
            and_any: '任一 AND',
            and_all: '所有 AND',
            not_any: '任一 NOT',
            not_all: '所有 NOT',
        },
    };
    // --- 应用程序状态 ---
    const appState = {
        regexes: { global: [], character: [] },
        lorebooks: { character: [] },
        chatLorebook: null,
        allLorebooks: [],
        lorebookEntries: new Map(),
        lorebookUsage: new Map(),
        activeTab: 'global-lore',
        isDataLoaded: false,
        searchFilters: { bookName: true, entryName: true, keywords: true, content: true },
        multiSelectMode: false,
        selectedItems: new Set(),
    };
    // --- 全局变量 ---
    let parentWin;
    let $;
    let TavernHelper;
    /**
     * 等待DOM和API就绪
     */
    function onReady(callback) {
        const domSelector = '#extensionsMenu';
        const maxRetries = 100;
        let retries = 0;
        console.log(`[WorldInfoOptimizer] Starting readiness check. Polling for DOM element "${domSelector}" AND core APIs.`);
        const interval = setInterval(() => {
            const parentDoc = window.parent.document;
            parentWin = window.parent;
            const domReady = parentDoc.querySelector(domSelector) !== null;
            const apiReady = parentWin.TavernHelper && typeof parentWin.TavernHelper.getCharData === 'function' && parentWin.jQuery;
            if (domReady && apiReady) {
                clearInterval(interval);
                console.log(`[WorldInfoOptimizer] SUCCESS: Both DOM and Core APIs are ready. Initializing script.`);
                try {
                    callback(parentWin.jQuery, parentWin.TavernHelper);
                }
                catch (e) {
                    console.error('[WorldInfoOptimizer] FATAL: Error during main callback execution.', e);
                }
            }
            else {
                retries++;
                if (retries > maxRetries) {
                    clearInterval(interval);
                    console.error(`[WorldInfoOptimizer] FATAL: Readiness check timed out.`);
                    if (!domReady)
                        console.error(`[WorldInfoOptimizer] -> Failure: DOM element "${domSelector}" not found.`);
                    if (!apiReady)
                        console.error(`[WorldInfoOptimizer] -> Failure: Core APIs not available.`);
                }
            }
        }, 150);
    }
    /**
     * 错误处理包装器
     */
    const errorCatched = (fn, context = 'WorldInfoOptimizer') => async (...args) => {
        try {
            return await fn(...args);
        }
        catch (error) {
            if (error) {
                console.error(`[${context}] Error:`, error);
                await showModal({
                    type: 'alert',
                    title: '脚本异常',
                    text: `操作中发生未知错误，请检查开发者控制台获取详细信息。`,
                });
            }
        }
    };
    // --- 安全访问 lorebookEntries 的函数 ---
    const safeGetLorebookEntries = (bookName) => {
        try {
            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
                appState.lorebookEntries = new Map();
            }
            if (typeof appState.lorebookEntries.get !== 'function') {
                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.get is not a function, reinitializing...');
                appState.lorebookEntries = new Map();
            }
            const entries = appState.lorebookEntries.get(bookName);
            return Array.isArray(entries) ? entries : [];
        }
        catch (error) {
            console.error('[WorldInfoOptimizer] Error in safeGetLorebookEntries:', error);
            appState.lorebookEntries = new Map();
            return [];
        }
    };
    const safeSetLorebookEntries = (bookName, entries) => {
        try {
            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
                appState.lorebookEntries = new Map();
            }
            if (typeof appState.lorebookEntries.set !== 'function') {
                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.set is not a function, reinitializing...');
                appState.lorebookEntries = new Map();
            }
            appState.lorebookEntries.set(bookName, Array.isArray(entries) ? entries : []);
        }
        catch (error) {
            console.error('[WorldInfoOptimizer] Error in safeSetLorebookEntries:', error);
            appState.lorebookEntries = new Map();
            appState.lorebookEntries.set(bookName, Array.isArray(entries) ? entries : []);
        }
    };
    const safeDeleteLorebookEntries = (bookName) => {
        try {
            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
                appState.lorebookEntries = new Map();
                return;
            }
            if (typeof appState.lorebookEntries.delete !== 'function') {
                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.delete is not a function, reinitializing...');
                appState.lorebookEntries = new Map();
                return;
            }
            appState.lorebookEntries.delete(bookName);
        }
        catch (error) {
            console.error('[WorldInfoOptimizer] Error in safeDeleteLorebookEntries:', error);
            appState.lorebookEntries = new Map();
        }
    };
    const safeClearLorebookEntries = () => {
        try {
            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
                appState.lorebookEntries = new Map();
                return;
            }
            if (typeof appState.lorebookEntries.clear !== 'function') {
                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.clear is not a function, reinitializing...');
                appState.lorebookEntries = new Map();
                return;
            }
            appState.lorebookEntries.clear();
        }
        catch (error) {
            console.error('[WorldInfoOptimizer] Error in safeClearLorebookEntries:', error);
            appState.lorebookEntries = new Map();
        }
    };
    const safeHasLorebookEntries = (bookName) => {
        try {
            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
                appState.lorebookEntries = new Map();
                return false;
            }
            if (typeof appState.lorebookEntries.has !== 'function') {
                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.has is not a function, reinitializing...');
                appState.lorebookEntries = new Map();
                return false;
            }
            return appState.lorebookEntries.has(bookName);
        }
        catch (error) {
            console.error('[WorldInfoOptimizer] Error in safeHasLorebookEntries:', error);
            appState.lorebookEntries = new Map();
            return false;
        }
    };
    // --- 工具函数 ---
    const escapeHtml = (text) => {
        if (typeof text !== 'string')
            return String(text);
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    };
    // 处理文本内容并保留换行符
    const escapeHtmlWithNewlines = (text) => {
        const escaped = escapeHtml(text);
        return escaped.replace(/\n/g, '<br>');
    };
    const unescapeHtml = (html) => {
        if (typeof html !== 'string')
            return String(html);
        const area = document.createElement('textarea');
        area.innerHTML = html;
        return area.value;
    };
    const highlightText = (text, searchTerm) => {
        if (!text)
            return '';
        // 移除任何现有的高亮标签，防止嵌套
        const cleanText = text.replace(/<\/?mark\s+class="wio-highlight">/gi, '');
        // 如果没有搜索词，只返回清理后的纯文本（不需要HTML转义）
        if (!searchTerm.trim()) {
            return cleanText;
        }
        // Escape special characters in search term for regex.
        const escapedSearchTerm = searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        try {
            const regex = new RegExp(`(${escapedSearchTerm})`, 'gi');
            // Split the text by the search term, keeping the delimiter.
            const parts = cleanText.split(regex);
            // Process parts: escape normal text, wrap matches in <mark>.
            return parts
                .map((part, index) => {
                if (!part)
                    return '';
                // The matched parts are at odd indices (1, 3, 5, ...).
                if (index % 2 === 1) {
                    return `<mark class="wio-highlight">${escapeHtml(part)}</mark>`;
                }
                else {
                    return escapeHtml(part);
                }
            })
                .join('');
        }
        catch (e) {
            console.warn(`[WorldInfoOptimizer] Invalid regex for highlighting: "${escapedSearchTerm}"`, e);
            // Fallback to just escaping the text on error.
            return escapeHtml(cleanText);
        }
    };
    const isMatch = (text, searchTerm) => {
        if (!searchTerm.trim())
            return false;
        if (!text)
            return false;
        try {
            // 首先尝试简单的文本包含检查（对JSON更友好）
            if (text.toLowerCase().includes(searchTerm.toLowerCase())) {
                return true;
            }
            // 然后尝试正则匹配（处理转义后的搜索词）
            // 转义所有正则特殊字符，包括引号
            const escapedSearchTerm = searchTerm.replace(/[.*+?^${}()|[\]\\"]/g, '\\$&');
            const regex = new RegExp(escapedSearchTerm, 'i');
            return regex.test(text);
        }
        catch (e) {
            console.warn(`[WorldInfoOptimizer] Invalid regex created from search term: "${searchTerm}"`, e);
            // 作为备选方案，如果正则表达式无效，则退回到简单的文本搜索
            return text.toLowerCase().includes(searchTerm.toLowerCase());
        }
    };
    // --- 通知和模态框函数 ---
    const showSuccessTick = (message = '操作成功', duration = 1500) => {
        const $panel = $(`#${PANEL_ID}`, parentWin.document);
        if ($panel.length === 0)
            return;
        $panel.find('.wio-toast-notification').remove();
        const toastHtml = `<div class="wio-toast-notification"><i class="fa-solid fa-check-circle"></i> ${escapeHtml(message)}</div>`;
        const $toast = $(toastHtml);
        $panel.append($toast);
        setTimeout(() => {
            $toast.addClass('visible');
        }, 10);
        setTimeout(() => {
            $toast.removeClass('visible');
            setTimeout(() => {
                $toast.remove();
            }, 300);
        }, duration);
    };
    const showProgressToast = (initialMessage = '正在处理...') => {
        const $panel = $(`#${PANEL_ID}`, parentWin.document);
        if ($panel.length === 0)
            return { update: () => { }, remove: () => { } };
        $panel.find('.wio-progress-toast').remove();
        const toastHtml = `<div class="wio-progress-toast"><i class="fa-solid fa-spinner fa-spin"></i> <span class="wio-progress-text">${escapeHtml(initialMessage)}</span></div>`;
        const $toast = $(toastHtml);
        $panel.append($toast);
        setTimeout(() => {
            $toast.addClass('visible');
        }, 10);
        const update = (newMessage) => {
            $toast.find('.wio-progress-text').html(escapeHtml(newMessage));
        };
        const remove = () => {
            $toast.removeClass('visible');
            setTimeout(() => {
                $toast.remove();
            }, 300);
        };
        return { update, remove };
    };
    const showModal = (options) => {
        return new Promise((resolve, reject) => {
            const { type = 'alert', title = '通知', text = '', placeholder = '', value = '' } = options;
            let buttonsHtml = '';
            if (type === 'alert')
                buttonsHtml = '<button class="wio-modal-btn wio-modal-ok">确定</button>';
            else if (type === 'confirm')
                buttonsHtml =
                    '<button class="wio-modal-btn wio-modal-cancel">取消</button><button class="wio-modal-btn wio-modal-ok">确认</button>';
            else if (type === 'prompt')
                buttonsHtml =
                    '<button class="wio-modal-btn wio-modal-cancel">取消</button><button class="wio-modal-btn wio-modal-ok">确定</button>';
            const inputHtml = type === 'prompt'
                ? `<input type="text" class="wio-modal-input" placeholder="${escapeHtml(placeholder)}" value="${escapeHtml(value)}">`
                : '';
            const modalHtml = `<div class="wio-modal-overlay"><div class="wio-modal-content"><div class="wio-modal-header">${escapeHtml(title)}</div><div class="wio-modal-body"><p>${escapeHtmlWithNewlines(text)}</p>${inputHtml}</div><div class="wio-modal-footer">${buttonsHtml}</div></div></div>`;
            const $modal = $(modalHtml).hide();
            const $panel = $(`#${PANEL_ID}`, parentWin.document);
            if ($panel.length > 0) {
                $panel.append($modal);
            }
            else {
                $('body', parentWin.document).append($modal);
            }
            $modal.fadeIn(200);
            const $input = $modal.find('.wio-modal-input');
            if (type === 'prompt')
                $input.focus().select();
            const closeModal = (isSuccess, val) => {
                $modal.fadeOut(200, () => {
                    $modal.remove();
                    if (isSuccess)
                        resolve(val);
                    else
                        reject();
                });
            };
            $modal.on('click', '.wio-modal-ok', () => {
                const val = type === 'prompt' ? $input.val() : true;
                if (type === 'prompt' && !String(val).trim()) {
                    $input.addClass('wio-input-error');
                    setTimeout(() => $input.removeClass('wio-input-error'), 500);
                    return;
                }
                closeModal(true, val);
            });
            $modal.on('click', '.wio-modal-cancel', () => closeModal(false));
            if (type === 'prompt') {
                $input.on('keydown', (e) => {
                    if (e.key === 'Enter')
                        $modal.find('.wio-modal-ok').click();
                    else if (e.key === 'Escape')
                        closeModal(false);
                });
            }
        });
    };
    // --- API 包装器 ---
    let TavernAPI = null;
    const initializeTavernAPI = () => {
        TavernAPI = {
            createLorebook: errorCatched(async (name) => await TavernHelper.createLorebook(name)),
            deleteLorebook: errorCatched(async (name) => await TavernHelper.deleteLorebook(name)),
            getLorebooks: errorCatched(async () => await TavernHelper.getLorebooks()),
            setLorebookSettings: errorCatched(async (settings) => await TavernHelper.setLorebookSettings(settings)),
            getCharData: errorCatched(async () => await TavernHelper.getCharData()),
            Character: TavernHelper.Character || null,
            getRegexes: errorCatched(async () => await TavernHelper.getTavernRegexes({ scope: 'all' })),
            replaceRegexes: errorCatched(async (regexes) => await TavernHelper.replaceTavernRegexes(regexes, { scope: 'all' })),
            getLorebookSettings: errorCatched(async () => await TavernHelper.getLorebookSettings()),
            getCharLorebooks: errorCatched(async (charData) => await TavernHelper.getCharLorebooks(charData)),
            getCurrentCharLorebooks: errorCatched(async () => await TavernHelper.getCharLorebooks()),
            getChatLorebook: errorCatched(async () => await TavernHelper.getChatLorebook()),
            getOrCreateChatLorebook: errorCatched(async (name) => await TavernHelper.getOrCreateChatLorebook(name)),
            setChatLorebook: errorCatched(async (name) => await TavernHelper.setChatLorebook(name)),
            getLorebookEntries: errorCatched(async (name) => await TavernHelper.getLorebookEntries(name)),
            setLorebookEntries: errorCatched(async (name, entries) => await TavernHelper.setLorebookEntries(name, entries)),
            createLorebookEntries: errorCatched(async (name, entries) => await TavernHelper.createLorebookEntries(name, entries)),
            deleteLorebookEntries: errorCatched(async (name, uids) => await TavernHelper.deleteLorebookEntries(name, uids)),
            saveSettings: errorCatched(async () => await TavernHelper.builtin.saveSettings()),
            setCurrentCharLorebooks: errorCatched(async (lorebooks) => await TavernHelper.setCurrentCharLorebooks(lorebooks)),
        };
    };
    // --- 数据加载函数 ---
    const loadAllData = errorCatched(async () => {
        const $content = $(`#${PANEL_ID}-content`, parentWin.document);
        $content.html(`
            <div class="wio-loading-container">
                <div class="wio-loading-title">数据同步中...</div>
                <div class="wio-loading-progress-bar-container">
                    <div id="wio-loading-bar" class="wio-loading-progress-bar" style="width: 0%;"></div>
                </div>
                <div id="wio-loading-status" class="wio-loading-status-text">正在初始化...</div>
            </div>
        `);
        const $progressBar = $('#wio-loading-bar', parentWin.document);
        const $statusText = $('#wio-loading-status', parentWin.document);
        const updateProgress = (percentage, text) => {
            if ($progressBar.length)
                $progressBar.css('width', `${Math.min(100, Math.max(0, percentage))}%`);
            if ($statusText.length)
                $statusText.text(text);
        };
        try {
            updateProgress(5, '正在连接 SillyTavern API...');
            // 防御性检查：确保SillyTavern API可用
            if (!parentWin.SillyTavern || !parentWin.SillyTavern.getContext) {
                console.warn('[WorldInfoOptimizer] SillyTavern API not available, initializing with empty data');
                appState.regexes.global = [];
                appState.regexes.character = [];
                appState.allLorebooks = [];
                appState.lorebooks.character = [];
                appState.chatLorebook = null;
                safeClearLorebookEntries();
                appState.isDataLoaded = true;
                renderContent();
                return;
            }
            const context = parentWin.SillyTavern.getContext() || {};
            const allCharacters = Array.isArray(context.characters) ? context.characters : [];
            const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;
            const hasActiveChat = context.chatId !== undefined && context.chatId !== null;
            let charData = null, charLinkedBooks = null, chatLorebook = null;
            updateProgress(10, '正在获取核心设置...');
            // 使用Promise.allSettled来避免单个失败影响整体
            const promises = [
                TavernAPI.getRegexes().catch(() => []),
                TavernAPI.getLorebookSettings().catch(() => ({})),
                TavernAPI.getLorebooks().catch(() => []),
            ];
            if (hasActiveCharacter) {
                promises.push(TavernAPI.getCharData().catch(() => null));
                promises.push(TavernAPI.getCurrentCharLorebooks().catch(() => null));
            }
            else {
                promises.push(Promise.resolve(null), Promise.resolve(null));
            }
            if (hasActiveChat) {
                promises.push(TavernAPI.getChatLorebook().catch((error) => {
                    console.warn('[WorldInfoOptimizer] Failed to get chat lorebook:', error);
                    return null;
                }));
            }
            else {
                promises.push(Promise.resolve(null));
            }
            const results = await Promise.allSettled(promises);
            updateProgress(20, '核心数据已获取，正在分析...');
            // 安全提取结果
            const allUIRegexes = results[0].status === 'fulfilled' ? results[0].value : [];
            const globalSettings = results[1].status === 'fulfilled' ? results[1].value : {};
            const allBookFileNames = results[2].status === 'fulfilled' ? results[2].value : [];
            charData = results[3]?.status === 'fulfilled' ? results[3].value : null;
            charLinkedBooks = results[4]?.status === 'fulfilled' ? results[4].value : null;
            chatLorebook = results[5]?.status === 'fulfilled' ? results[5].value : null;
            appState.regexes.global = Array.isArray(allUIRegexes)
                ? allUIRegexes.filter((r) => r.scope === 'global')
                : [];
            updateCharacterRegexes(allUIRegexes, charData);
            safeClearLorebookEntries();
            appState.lorebookUsage.clear();
            const knownBookNames = new Set(Array.isArray(allBookFileNames) ? allBookFileNames : []);
            updateProgress(30, '正在分析角色数据...');
            // 安全处理角色世界书
            if (Array.isArray(allCharacters) && allCharacters.length > 0) {
                try {
                    // No need for Promise.all here as it's not a bottleneck and makes progress tracking complex
                    for (let i = 0; i < allCharacters.length; i++) {
                        const char = allCharacters[i];
                        if (!char || !char.name)
                            continue;
                        try {
                            let books = null;
                            try {
                                const result = TavernHelper.getCharLorebooks({ name: char.name });
                                books = result && typeof result.then === 'function' ? await result : result;
                            }
                            catch (error) {
                                console.warn(`[WorldInfoOptimizer] Error getting lorebooks for character "${char.name}":`, error);
                            }
                            if (books && typeof books === 'object') {
                                const bookSet = new Set();
                                if (books.primary && typeof books.primary === 'string')
                                    bookSet.add(books.primary);
                                if (Array.isArray(books.additional)) {
                                    books.additional.forEach((b) => typeof b === 'string' && bookSet.add(b));
                                }
                                bookSet.forEach(bookName => {
                                    if (typeof bookName === 'string') {
                                        if (!appState.lorebookUsage.has(bookName)) {
                                            appState.lorebookUsage.set(bookName, []);
                                        }
                                        appState.lorebookUsage.get(bookName).push(char.name);
                                        knownBookNames.add(bookName);
                                    }
                                });
                            }
                        }
                        catch (charError) {
                            console.warn(`[WorldInfoOptimizer] Error processing character ${char.name}:`, charError);
                        }
                        const charProgress = 30 + (i / allCharacters.length) * 20; // This stage takes 20%
                        updateProgress(charProgress, `正在分析角色: ${char.name}`);
                    }
                }
                catch (charProcessingError) {
                    console.warn('[WorldInfoOptimizer] Error processing characters:', charProcessingError);
                }
            }
            updateProgress(50, '角色数据分析完毕，正在整理世界书列表...');
            const enabledGlobalBooks = new Set(Array.isArray(globalSettings?.selected_global_lorebooks) ? globalSettings.selected_global_lorebooks : []);
            appState.allLorebooks = (Array.isArray(allBookFileNames) ? allBookFileNames : []).map((name) => ({
                name: name,
                enabled: enabledGlobalBooks.has(name),
            }));
            const charBookSet = new Set();
            if (charLinkedBooks && typeof charLinkedBooks === 'object') {
                if (charLinkedBooks.primary && typeof charLinkedBooks.primary === 'string') {
                    charBookSet.add(charLinkedBooks.primary);
                }
                if (Array.isArray(charLinkedBooks.additional)) {
                    charLinkedBooks.additional.forEach((name) => typeof name === 'string' && charBookSet.add(name));
                }
            }
            appState.lorebooks.character = Array.from(charBookSet);
            appState.chatLorebook = typeof chatLorebook === 'string' ? chatLorebook : null;
            if (typeof chatLorebook === 'string') {
                knownBookNames.add(chatLorebook);
            }
            const allBooksToLoad = Array.from(knownBookNames);
            const existingBookFiles = new Set(Array.isArray(allBookFileNames) ? allBookFileNames : []);
            updateProgress(55, `准备加载 ${allBooksToLoad.length} 个世界书的条目...`);
            // 分批加载世界书条目
            const batchSize = 5;
            for (let i = 0; i < allBooksToLoad.length; i += batchSize) {
                const batch = allBooksToLoad.slice(i, i + batchSize);
                await Promise.allSettled(batch.map(async (name) => {
                    if (existingBookFiles.has(name) && typeof name === 'string') {
                        try {
                            const entries = await TavernAPI.getLorebookEntries(name).catch(() => []);
                            safeSetLorebookEntries(name, entries);
                        }
                        catch (entryError) {
                            console.warn(`[WorldInfoOptimizer] Error loading entries for book ${name}:`, entryError);
                        }
                    }
                }));
                const bookProgress = 55 + ((i + batch.length) / allBooksToLoad.length) * 40; // This stage takes 40%
                updateProgress(bookProgress, `已加载 ${i + batch.length} / ${allBooksToLoad.length} 个世界书`);
            }
            updateProgress(100, '加载完成，正在渲染界面...');
            appState.isDataLoaded = true;
            renderContent();
        }
        catch (error) {
            console.error('[WorldInfoOptimizer] Error in loadAllData:', error);
            $content.html(`
                <div style="padding: 20px; text-align: center;">
                    <p style="color: #ff6b6b; margin-bottom: 10px;">
                        <i class="fa-solid fa-exclamation-triangle"></i> 数据加载失败
                    </p>
                    <p style="color: #666; font-size: 14px;">
                        请检查开发者控制台获取详细信息，或尝试刷新页面。
                    </p>
                    <button class="wio-modal-btn" onclick="$('#${REFRESH_BTN_ID}').click()"
                            style="margin-top: 15px; padding: 8px 16px;">
                        <i class="fa-solid fa-refresh"></i> 重试
                    </button>
                </div>
            `);
            throw error;
        }
    });
    // --- 角色正则和世界书更新函数 ---
    function updateCharacterRegexes(allUIRegexes, charData) {
        const characterUIRegexes = allUIRegexes?.filter((r) => r.scope === 'character') || [];
        let cardRegexes = [];
        if (charData && TavernAPI && TavernAPI.Character) {
            try {
                const character = new TavernAPI.Character(charData);
                cardRegexes = (character.getRegexScripts() || []).map((r, i) => ({
                    id: r.id || `card-${Date.now()}-${i}`,
                    script_name: r.scriptName || '未命名卡内正则',
                    find_regex: r.findRegex,
                    replace_string: r.replaceString,
                    enabled: !r.disabled,
                    scope: 'character',
                    source: 'card',
                }));
            }
            catch (e) {
                console.warn('无法解析角色卡正则脚本:', e);
            }
        }
        const uiRegexIdentifiers = new Set(characterUIRegexes.map((r) => `${r.script_name}::${r.find_regex}::${r.replace_string}`));
        const uniqueCardRegexes = cardRegexes.filter((r) => {
            const identifier = `${r.script_name}::${r.find_regex}::${r.replace_string}`;
            return !uiRegexIdentifiers.has(identifier);
        });
        appState.regexes.character = [...characterUIRegexes, ...uniqueCardRegexes];
    }
    function updateCharacterLorebooks(charBooks) {
        const characterBookNames = [];
        if (charBooks) {
            if (charBooks.primary)
                characterBookNames.push(charBooks.primary);
            if (charBooks.additional)
                characterBookNames.push(...charBooks.additional);
        }
        appState.lorebooks.character = [...new Set(characterBookNames)];
    }
    /**
     * 统一的数据筛选与处理函数
     * @param books - 要处理的世界书数组 (例如 appState.allLorebooks)
     * @param searchTerm - 当前搜索词
     * @returns 过滤和处理后的数据，包含是否展开的标志
     */
    const getFilteredLorebookData = (books, searchTerm) => {
        if (!searchTerm) {
            return books.map(book => ({
                book,
                entries: [...safeGetLorebookEntries(book.name)],
                shouldExpand: false, // 无搜索词时不自动展开
            }));
        }
        const filteredData = [];
        books.forEach(book => {
            const entries = [...safeGetLorebookEntries(book.name)];
            const bookNameMatches = appState.searchFilters.bookName && isMatch(book.name, searchTerm);
            const matchingEntries = entries.filter(entry => (appState.searchFilters.entryName && isMatch(entry.comment || '', searchTerm)) ||
                (appState.searchFilters.keywords && isMatch(entry.keys.join(' '), searchTerm)) ||
                (appState.searchFilters.content && entry.content && isMatch(entry.content, searchTerm)));
            if (bookNameMatches || matchingEntries.length > 0) {
                filteredData.push({
                    book,
                    // 如果书名匹配，显示所有条目；否则只显示匹配的条目
                    entries: bookNameMatches ? entries : matchingEntries,
                    // 核心逻辑：如果书名匹配或有条目匹配，则应展开
                    shouldExpand: true,
                });
            }
        });
        return filteredData;
    };
    // --- 渲染函数 ---
    const renderContent = () => {
        // 根据全局状态同步所有多选按钮的UI
        const $multiSelectToggles = $(`.wio-multi-select-toggle`, parentWin.document);
        if (appState.multiSelectMode) {
            $multiSelectToggles.addClass('active').html('<i class="fa-solid fa-times"></i> 退出多选');
        }
        else {
            $multiSelectToggles.removeClass('active').html('<i class="fa-solid fa-check-square"></i> 多选模式');
        }
        const searchTerm = $(`#${SEARCH_INPUT_ID}`, parentWin.document).val() || '';
        appState.searchFilters.bookName = $(`#wio-filter-book-name`, parentWin.document).is(':checked');
        appState.searchFilters.entryName = $(`#wio-filter-entry-name`, parentWin.document).is(':checked');
        appState.searchFilters.keywords = $(`#wio-filter-keywords`, parentWin.document).is(':checked');
        appState.searchFilters.content = $(`#wio-filter-content`, parentWin.document).is(':checked');
        const $content = $(`#${PANEL_ID}-content`, parentWin.document);
        $content.empty();
        $(`#${PANEL_ID}`, parentWin.document).toggleClass('wio-multi-select-mode', appState.multiSelectMode);
        const isLoreTab = appState.activeTab === 'global-lore' || appState.activeTab === 'char-lore' || appState.activeTab === 'chat-lore';
        $(`#wio-search-filters-container`, parentWin.document).toggle(isLoreTab);
        const isAnyEditing = appState.multiSelectMode || $('.wio-book-group.editing-entries', parentWin.document).length > 0;
        $(`#wio-multi-select-controls`, parentWin.document).toggle(isAnyEditing);
        updateSelectionCount();
        switch (appState.activeTab) {
            case 'global-lore':
                renderGlobalLorebookView(searchTerm, $content);
                break;
            case 'char-lore':
                renderCharacterLorebookView(searchTerm, $content);
                break;
            case 'chat-lore':
                renderChatLorebookView(searchTerm, $content);
                break;
            case 'global-regex':
                renderRegexView(appState.regexes.global, searchTerm, $content, '全局正则');
                break;
            case 'char-regex':
                renderRegexView(appState.regexes.character, searchTerm, $content, '角色正则');
                break;
        }
    };
    // --- 选择和批量操作函数 ---
    const getAllVisibleItems = () => {
        const visibleItems = [];
        const activeTab = appState.activeTab;
        if (activeTab === 'global-lore') {
            appState.allLorebooks.forEach(book => {
                visibleItems.push({ type: 'book', id: book.name, enabled: book.enabled });
                [...safeGetLorebookEntries(book.name)].forEach(entry => {
                    visibleItems.push({ type: 'lore', id: entry.uid, bookName: book.name, enabled: entry.enabled });
                });
            });
        }
        else if (activeTab === 'char-lore') {
            appState.lorebooks.character.forEach(bookName => {
                [...safeGetLorebookEntries(bookName)].forEach(entry => {
                    visibleItems.push({ type: 'lore', id: entry.uid, bookName, enabled: entry.enabled });
                });
            });
        }
        else if (activeTab === 'global-regex') {
            appState.regexes.global.forEach(regex => {
                visibleItems.push({ type: 'regex', id: regex.id, enabled: regex.enabled });
            });
        }
        else if (activeTab === 'char-regex') {
            appState.regexes.character.forEach(regex => {
                visibleItems.push({ type: 'regex', id: regex.id, enabled: regex.enabled });
            });
        }
        return visibleItems;
    };
    const renderGlobalLorebookView = (searchTerm, $container) => {
        const books = [...appState.allLorebooks].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.name.localeCompare(b.name));
        // 调用新的辅助函数来处理数据
        const filteredBookData = getFilteredLorebookData(books, searchTerm);
        if (filteredBookData.length === 0 && appState.allLorebooks.length > 0) {
            $container.html(`<p class="wio-info-text">未找到匹配的世界书。</p>`);
        }
        else if (appState.allLorebooks.length === 0) {
            $container.html(`<p class="wio-info-text">还没有世界书，点击上方"+"创建一个吧。</p>`);
        }
        filteredBookData.forEach(data => {
            if (data && data.book) {
                $container.append(createGlobalLorebookElement(data.book, data.entries, searchTerm, data.shouldExpand));
            }
        });
    };
    const renderCharacterLorebookView = (searchTerm, $container) => {
        const linkedBooks = appState.lorebooks.character;
        const context = parentWin.SillyTavern.getContext();
        const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;
        if (!hasActiveCharacter) {
            $container.html(`<p class="wio-info-text">请先加载一个角色以管理角色世界书。</p>`);
            return;
        }
        if (linkedBooks.length === 0) {
            $container.html(`<p class="wio-info-text">当前角色没有绑定的世界书。点击同步按钮刷新。</p>`);
            return;
        }
        // 转换数据格式以复用 getFilteredLorebookData
        const booksForFiltering = linkedBooks.map(name => ({ name }));
        const filteredBookData = getFilteredLorebookData(booksForFiltering, searchTerm);
        if (filteredBookData.length === 0 && searchTerm) {
            $container.html(`<p class="wio-info-text">未找到匹配的世界书或条目。</p>`);
            return;
        }
        filteredBookData.forEach(data => {
            const { book, entries, shouldExpand } = data;
            const bookName = book.name;
            const $bookContainer = $(`
        <div class="wio-book-group" data-book-name="${escapeHtml(bookName)}">
          <div class="wio-book-group-header">
            <span>${highlightText(bookName, searchTerm)}</span>
            <div class="wio-item-controls">
              <button class="wio-action-btn-icon wio-rename-book-btn" title="重命名世界书"><i class="fa-solid fa-pencil"></i></button>
              <button class="wio-action-btn-icon wio-edit-entries-btn" title="多选条目"><i class="fa-solid fa-list-check"></i></button>
              <button class="wio-action-btn-icon wio-delete-book-btn" title="删除世界书"><i class="fa-solid fa-trash-can"></i></button>
            </div>
          </div>
          <div class="wio-collapsible-content" style="${shouldExpand ? 'display: block;' : ''}">
            <div class="wio-entry-list-wrapper"></div>
          </div>
        </div>
      `);
            const $listWrapper = $bookContainer.find('.wio-entry-list-wrapper');
            const $entryActions = $(`<div class="wio-entry-actions"><button class="wio-action-btn wio-create-entry-btn" data-book-name="${escapeHtml(bookName)}"><i class="fa-solid fa-plus"></i> 新建条目</button><button class="wio-action-btn wio-batch-recursion-btn" data-book-name="${escapeHtml(bookName)}"><i class="fa-solid fa-shield-halved"></i> 全开防递</button><button class="wio-action-btn wio-fix-keywords-btn" data-book-name="${escapeHtml(bookName)}"><i class="fa-solid fa-check-double"></i> 修复关键词</button></div>`);
            $listWrapper.append($entryActions);
            const sortedEntries = [...entries].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);
            if (sortedEntries.length === 0 && searchTerm) {
                $listWrapper.append(`<p class="wio-info-text-small">无匹配条目</p>`);
            }
            else {
                sortedEntries.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', bookName, searchTerm)));
            }
            $container.append($bookContainer);
        });
    };
    const renderChatLorebookView = (searchTerm, $container) => {
        const bookName = appState.chatLorebook;
        const context = parentWin.SillyTavern.getContext();
        const hasActiveChat = context.chatId !== undefined && context.chatId !== null;
        if (!hasActiveChat) {
            $container.html(`<p class="wio-info-text">请先开始一个聊天以管理聊天世界书。</p>`);
            return;
        }
        if (!bookName) {
            $container.html(`
        <div class="wio-info-section">
          <p class="wio-info-text">当前聊天没有绑定世界书。</p>
          <button id="wio-create-chat-lore-btn" class="wio-btn wio-btn-primary">
            <i class="fa-solid fa-plus"></i> 创建聊天世界书
          </button>
        </div>
      `);
            return;
        }
        // 复用 getFilteredLorebookData
        const booksForFiltering = [{ name: bookName }];
        const filteredBookData = getFilteredLorebookData(booksForFiltering, searchTerm);
        if (filteredBookData.length === 0) {
            // 即使没有匹配项，如果存在聊天世界书，也应该显示其框架
            filteredBookData.push({
                book: { name: bookName },
                entries: [],
                shouldExpand: false,
            });
        }
        const { book, entries, shouldExpand } = filteredBookData[0];
        const $bookContainer = $(`
      <div class="wio-book-group" data-book-name="${escapeHtml(bookName)}">
        <div class="wio-book-group-header">
          <span>${highlightText(bookName, searchTerm)} (聊天世界书)</span>
          <div class="wio-item-controls">
            <button class="wio-action-btn-icon wio-edit-entries-btn" title="多选条目"><i class="fa-solid fa-list-check"></i></button>
            <button class="wio-action-btn-icon wio-unlink-chat-lore-btn" title="解除绑定"><i class="fa-solid fa-unlink"></i></button>
          </div>
        </div>
        <div class="wio-collapsible-content" style="${shouldExpand ? 'display: block;' : ''}">
           <div class="wio-entry-list-wrapper"></div>
        </div>
      </div>
    `);
        const $listWrapper = $bookContainer.find('.wio-entry-list-wrapper');
        const $entryActions = $(`<div class="wio-entry-actions"><button class="wio-action-btn wio-create-entry-btn" data-book-name="${escapeHtml(bookName)}"><i class="fa-solid fa-plus"></i> 新建条目</button></div>`);
        $listWrapper.append($entryActions);
        const sortedEntries = [...entries].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);
        if (sortedEntries.length === 0 && searchTerm) {
            $listWrapper.append(`<p class="wio-info-text-small">无匹配条目</p>`);
        }
        else {
            sortedEntries.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', bookName, searchTerm)));
        }
        $container.empty().append($bookContainer);
    };
    const renderRegexView = (regexes, searchTerm, $container, title) => {
        if (regexes.length === 0) {
            $container.html(`<p class="wio-info-text">没有找到${title}。</p>`);
            return;
        }
        // 按启用状态和名称排序
        const sortedRegexes = [...regexes].sort((a, b) => Number(b.enabled) - Number(a.enabled) || (a.script_name || '').localeCompare(b.script_name || ''));
        // 过滤匹配项
        let filteredRegexes = sortedRegexes;
        if (searchTerm) {
            filteredRegexes = sortedRegexes.filter(regex => {
                const name = regex.script_name || '';
                const findRegex = regex.find_regex || '';
                const replaceString = regex.replace_string || '';
                return (name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    findRegex.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    replaceString.toLowerCase().includes(searchTerm.toLowerCase()));
            });
        }
        if (filteredRegexes.length === 0 && searchTerm) {
            $container.html(`<p class="wio-info-text">未找到匹配的${title}。</p>`);
            return;
        }
        // 添加操作按钮区域
        const $actions = $(`
      <div class="wio-regex-actions">
        <button class="wio-action-btn wio-create-regex-btn" data-scope="${title === '全局正则' ? 'global' : 'character'}">
          <i class="fa-solid fa-plus"></i> 新建正则
        </button>
        <button class="wio-action-btn wio-import-regex-btn">
          <i class="fa-solid fa-upload"></i> 导入正则
        </button>
        <button class="wio-action-btn wio-export-regex-btn">
          <i class="fa-solid fa-download"></i> 导出正则
        </button>
        <button class="wio-btn wio-multi-select-toggle">
          <i class="fa-solid fa-check-square"></i> 多选模式
        </button>
      </div>
    `);
        $container.append($actions);
        // 渲染正则列表
        const $regexList = $('<div class="wio-regex-list"></div>');
        filteredRegexes.forEach((regex, index) => {
            const $element = createItemElement(regex, 'regex', '', searchTerm);
            // 添加序号指示器
            $element.find('.wio-item-name').prepend(`<span class="wio-order-indicator">#${index + 1}</span> `);
            $regexList.append($element);
        });
        $container.append($regexList);
        // 初始化拖拽排序（仅对非搜索状态的完整列表）
        if (!searchTerm && parentWin.Sortable) {
            const listEl = $regexList[0];
            if (listEl) {
                new parentWin.Sortable(listEl, {
                    animation: 150,
                    handle: '.wio-drag-handle',
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    onEnd: (evt) => handleRegexDragEnd(evt, title === '全局正则' ? 'global' : 'character'),
                });
            }
        }
    };
    // --- 核心UI元素创建函数 ---
    const createItemElement = (item, type, bookName = '', searchTerm = '') => {
        const isLore = type === 'lore';
        const id = isLore ? item.uid : item.id;
        const name = isLore ? item.comment || '无标题条目' : item.script_name || '未命名正则';
        const fromCard = item.source === 'card';
        let controlsHtml = '';
        if (isLore) {
            // 所有世界书条目都有完整的操作按钮
            controlsHtml = `
        <button class="wio-action-btn-icon wio-rename-btn" title="重命名"><i class="fa-solid fa-pencil"></i></button>
        <button class="wio-toggle-btn wio-item-toggle" title="启用/禁用此条目"><i class="fa-solid fa-power-off"></i></button>
        <button class="wio-action-btn-icon wio-delete-entry-btn" title="删除条目"><i class="fa-solid fa-trash-can"></i></button>
      `;
        }
        else if (fromCard) {
            // 来自卡片的正则只有开关
            controlsHtml =
                '<button class="wio-toggle-btn wio-item-toggle" title="启用/禁用此条目"><i class="fa-solid fa-power-off"></i></button>';
        }
        else {
            // UI中的正则有重命名和开关
            controlsHtml = `
        <button class="wio-action-btn-icon wio-rename-btn" title="重命名"><i class="fa-solid fa-pencil"></i></button>
        <button class="wio-toggle-btn wio-item-toggle" title="启用/禁用此条目"><i class="fa-solid fa-power-off"></i></button>
      `;
        }
        const dragHandleHtml = !fromCard && !isLore
            ? '<span class="wio-drag-handle" title="拖拽排序"><i class="fa-solid fa-grip-vertical"></i></span>'
            : '';
        // 应用高亮到条目名称
        const highlightedName = highlightText(name, searchTerm);
        const $element = $(`<div class="wio-item-container ${fromCard ? 'from-card' : ''}" data-type="${type}" data-id="${id}" ${isLore ? `data-book-name="${escapeHtml(bookName)}"` : ''}><div class="wio-item-header" title="${fromCard ? '此条目来自角色卡，部分操作受限' : appState.multiSelectMode ? '点击选择/取消选择' : '点击展开/编辑'}">${dragHandleHtml}<span class="wio-item-name">${highlightedName}</span><div class="wio-item-controls">${controlsHtml}</div></div><div class="wio-collapsible-content"></div></div>`);
        // 保存搜索词以便在内容展开时使用
        $element.data('searchTerm', searchTerm);
        $element.toggleClass('enabled', item.enabled);
        if (appState.multiSelectMode) {
            const itemKey = isLore ? `lore:${bookName}:${id}` : `regex:${id}`;
            $element.toggleClass('selected', appState.selectedItems.has(itemKey));
        }
        return $element;
    };
    const createGlobalLorebookElement = (book, entriesToShow, searchTerm, isExpanded) => {
        const usedByChars = appState.lorebookUsage.get(book.name) || [];
        const usedByHtml = usedByChars.length > 0
            ? `<div class="wio-used-by-chars">使用者: ${usedByChars.map(char => `<span>${escapeHtml(char)}</span>`).join(', ')}</div>`
            : '';
        const $element = $(`
      <div class="wio-book-group" data-book-name="${escapeHtml(book.name)}">
        <div class="wio-global-book-header" title="${appState.multiSelectMode ? '点击选择/取消选择' : '点击展开/折叠'}">
          <div class="wio-book-info">
            <span class="wio-book-name">${highlightText(book.name, searchTerm)}</span>
            <span class="wio-book-status ${book.enabled ? 'enabled' : 'disabled'}">${book.enabled ? '已启用' : '已禁用'}</span>
            ${usedByHtml}
          </div>
          <div class="wio-item-controls">
            <button class="wio-action-btn-icon wio-rename-book-btn" title="重命名世界书"><i class="fa-solid fa-pencil"></i></button>
            <button class="wio-action-btn-icon wio-edit-entries-btn" title="多选条目"><i class="fa-solid fa-list-check"></i></button>
            <button class="wio-action-btn-icon wio-delete-book-btn" title="删除世界书"><i class="fa-solid fa-trash-can"></i></button>
          </div>
        </div>
        <div class="wio-collapsible-content" style="${isExpanded ? 'display: block;' : ''}"></div>
      </div>
    `);
        const $content = $element.find('.wio-collapsible-content');
        // 添加条目操作按钮
        const $entryActions = $(`
      <div class="wio-entry-actions">
        <button class="wio-action-btn wio-create-entry-btn" data-book-name="${escapeHtml(book.name)}">
          <i class="fa-solid fa-plus"></i> 新建条目
        </button>
        <button class="wio-action-btn wio-batch-recursion-btn" data-book-name="${escapeHtml(book.name)}">
          <i class="fa-solid fa-shield-halved"></i> 全开防递
        </button>
        <button class="wio-action-btn wio-fix-keywords-btn" data-book-name="${escapeHtml(book.name)}">
          <i class="fa-solid fa-check-double"></i> 修复关键词
        </button>
      </div>
    `);
        $content.append($entryActions);
        const sortedEntries = [...entriesToShow].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);
        if (sortedEntries.length > 0) {
            const $listWrapper = $('<div class="wio-entry-list-wrapper"></div>');
            sortedEntries.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', book.name, searchTerm)));
            $content.append($listWrapper);
        }
        else if (searchTerm) {
            $content.append(`<div class="wio-info-text-small">无匹配项</div>`);
        }
        return $element;
    };
    // --- 替换功能实现 ---
    const handleReplace = errorCatched(async () => {
        const searchTerm = $(`#${SEARCH_INPUT_ID}`, parentWin.document).val();
        const replaceTerm = $('#wio-replace-input', parentWin.document).val();
        // 检查搜索词是否为空
        if (!searchTerm) {
            await showModal({ type: 'alert', title: '替换失败', text: '请先输入搜索词。' });
            return;
        }
        // 检查替换词是否为空
        if (!replaceTerm) {
            await showModal({ type: 'alert', title: '替换失败', text: '请先输入替换词。' });
            return;
        }
        // 获取当前视图的匹配项
        let matches = [];
        switch (appState.activeTab) {
            case 'global-lore':
                matches = getGlobalLorebookMatches(searchTerm);
                break;
            case 'char-lore':
                matches = getCharacterLorebookMatches(searchTerm);
                break;
            case 'chat-lore':
                matches = getChatLorebookMatches(searchTerm);
                break;
            default:
                await showModal({ type: 'alert', title: '替换失败', text: '替换功能仅支持世界书视图。' });
                return;
        }
        // 如果没有匹配项，提示用户
        if (matches.length === 0) {
            await showModal({ type: 'alert', title: '替换失败', text: '未找到匹配的条目。' });
            return;
        }
        // 显示确认对话框
        const confirmResult = await showModal({
            type: 'confirm',
            title: '确认替换',
            text: `找到 ${matches.length} 个匹配项。\n\n确定要将 "${searchTerm}" 替换为 "${replaceTerm}" 吗？\n\n注意：此操作仅替换条目的关键词、内容和条目名称，不会替换世界书本身的名称。\n此操作不可撤销，请谨慎操作。`,
        });
        // 如果用户确认替换，则执行替换
        if (confirmResult) {
            const progressToast = showProgressToast('正在执行替换...');
            try {
                await performReplace(matches, searchTerm, replaceTerm);
                progressToast.remove();
                showSuccessTick('替换完成');
                // 核心实现：将被替换文字放入搜索框，清空替换框
                $(`#${SEARCH_INPUT_ID}`, parentWin.document).val(replaceTerm);
                $('#wio-replace-input', parentWin.document).val('');
                // 重新初始化以获取最新数据并刷新视图
                await loadAllData();
            }
            catch (error) {
                progressToast.remove();
                console.error('[WorldInfoOptimizer] Replace error:', error);
                await showModal({
                    type: 'alert',
                    title: '替换失败',
                    text: '替换过程中发生错误，请检查开发者控制台获取详细信息。',
                });
            }
        }
    });
    // 执行替换操作的函数
    const performReplace = async (matches, searchTerm, replaceTerm) => {
        // 创建一个映射来跟踪每个世界书的更改
        const bookUpdates = new Map();
        // 遍历所有匹配项
        for (const match of matches) {
            const { bookName, entry } = match;
            let updated = false;
            // 如果还没有为这个世界书创建更新数组，则创建一个
            if (!bookUpdates.has(bookName)) {
                bookUpdates.set(bookName, []);
            }
            // 创建条目的深拷贝以进行修改
            const updatedEntry = JSON.parse(JSON.stringify(entry));
            // 替换关键词
            if (updatedEntry.keys && Array.isArray(updatedEntry.keys)) {
                const newKeys = updatedEntry.keys.map((key) => key.replace(new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replaceTerm));
                // 检查是否有实际更改
                if (JSON.stringify(updatedEntry.keys) !== JSON.stringify(newKeys)) {
                    updatedEntry.keys = newKeys;
                    updated = true;
                }
            }
            // 替换条目内容
            if (updatedEntry.content) {
                const newContent = updatedEntry.content.replace(new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replaceTerm);
                if (updatedEntry.content !== newContent) {
                    updatedEntry.content = newContent;
                    updated = true;
                }
            }
            // 替换条目名称（comment）
            if (updatedEntry.comment) {
                const newComment = updatedEntry.comment.replace(new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replaceTerm);
                if (updatedEntry.comment !== newComment) {
                    updatedEntry.comment = newComment;
                    updated = true;
                }
            }
            // 如果有更改，则将更新后的条目添加到更新数组中
            if (updated) {
                bookUpdates.get(bookName).push(updatedEntry);
            }
        }
        // 应用所有更改
        for (const [bookName, entriesToUpdate] of bookUpdates.entries()) {
            if (entriesToUpdate.length > 0) {
                // 调用TavernAPI来更新条目
                const result = await TavernAPI.setLorebookEntries(bookName, entriesToUpdate);
                if (result && result.entries) {
                    // 更新本地状态
                    safeSetLorebookEntries(bookName, result.entries);
                }
            }
        }
        // 等待一段时间以确保所有操作完成
        await new Promise(resolve => setTimeout(resolve, 100));
    };
    // 获取匹配项的函数
    const getGlobalLorebookMatches = (searchTerm) => {
        const matches = [];
        const books = [...appState.allLorebooks].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.name.localeCompare(b.name));
        if (!searchTerm) {
            // 如果没有搜索词，返回所有条目
            books.forEach(book => {
                const entries = [...safeGetLorebookEntries(book.name)];
                entries.forEach(entry => {
                    matches.push({ bookName: book.name, entry });
                });
            });
        }
        else {
            // 根据搜索词和过滤器获取匹配项
            books.forEach(book => {
                const entries = [...safeGetLorebookEntries(book.name)];
                const bookNameMatches = appState.searchFilters.bookName && isMatch(book.name, searchTerm);
                entries.forEach(entry => {
                    const entryNameMatches = appState.searchFilters.entryName && isMatch(entry.comment || '', searchTerm);
                    const keywordsMatch = appState.searchFilters.keywords && isMatch(entry.keys.join(' '), searchTerm);
                    const contentMatch = appState.searchFilters.content && entry.content && isMatch(entry.content, searchTerm);
                    // 如果书名匹配，或者条目名、关键词、内容中有任何一个匹配，则添加到匹配项中
                    if (bookNameMatches || entryNameMatches || keywordsMatch || contentMatch) {
                        matches.push({ bookName: book.name, entry });
                    }
                });
            });
        }
        return matches;
    };
    const getCharacterLorebookMatches = (searchTerm) => {
        const matches = [];
        const linkedBooks = appState.lorebooks.character;
        const context = parentWin.SillyTavern.getContext();
        const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;
        if (!hasActiveCharacter || linkedBooks.length === 0) {
            return matches;
        }
        linkedBooks.forEach(bookName => {
            const entries = [...safeGetLorebookEntries(bookName)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);
            if (!searchTerm) {
                // 如果没有搜索词，返回所有条目
                entries.forEach(entry => {
                    matches.push({ bookName, entry });
                });
            }
            else {
                // 根据搜索词和过滤器获取匹配项
                entries.forEach(entry => {
                    const bookNameMatches = appState.searchFilters.bookName && isMatch(bookName, searchTerm);
                    const entryNameMatches = appState.searchFilters.entryName && isMatch(entry.comment || '', searchTerm);
                    const keywordsMatch = appState.searchFilters.keywords && isMatch(entry.keys.join(' '), searchTerm);
                    const contentMatch = appState.searchFilters.content && entry.content && isMatch(entry.content, searchTerm);
                    // 如果书名匹配，或者条目名、关键词、内容中有任何一个匹配，则添加到匹配项中
                    if (bookNameMatches || entryNameMatches || keywordsMatch || contentMatch) {
                        matches.push({ bookName, entry });
                    }
                });
            }
        });
        return matches;
    };
    const getChatLorebookMatches = (searchTerm) => {
        const matches = [];
        const bookName = appState.chatLorebook;
        const context = parentWin.SillyTavern.getContext();
        const hasActiveChat = context.chatId !== undefined && context.chatId !== null;
        if (!hasActiveChat || !bookName) {
            return matches;
        }
        const entries = [...safeGetLorebookEntries(bookName)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);
        if (!searchTerm) {
            // 如果没有搜索词，返回所有条目
            entries.forEach(entry => {
                matches.push({ bookName, entry });
            });
        }
        else {
            // 根据搜索词和过滤器获取匹配项
            entries.forEach(entry => {
                const entryNameMatches = appState.searchFilters.entryName && isMatch(entry.comment || '', searchTerm);
                const keywordsMatch = appState.searchFilters.keywords && isMatch(entry.keys.join(' '), searchTerm);
                const contentMatch = appState.searchFilters.content && entry.content && isMatch(entry.content, searchTerm);
                // 如果条目名、关键词、内容中有任何一个匹配，则添加到匹配项中
                if (entryNameMatches || keywordsMatch || contentMatch) {
                    matches.push({ bookName, entry });
                }
            });
        }
        return matches;
    };
    // --- SortableJS 加载和拖拽排序功能 ---
    const loadSortableJS = (callback) => {
        if (parentWin.Sortable) {
            callback();
            return;
        }
        const script = parentWin.document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/sortablejs@1.15.2/Sortable.min.js';
        script.onload = () => {
            console.log('[WorldInfoOptimizer] SortableJS loaded successfully.');
            callback();
        };
        script.onerror = () => {
            console.error('[WorldInfoOptimizer] Failed to load SortableJS.');
            showModal({ type: 'alert', title: '错误', text: '无法加载拖拽排序库，请检查网络连接或浏览器控制台。' });
        };
        parentWin.document.head.appendChild(script);
    };
    // 防抖函数
    const debounce = (func, delay) => {
        let timeout;
        return (...args) => {
            clearTimeout(timeout);
            timeout = setTimeout(() => func(...args), delay);
        };
    };
    // 防抖保存正则顺序
    const debouncedSaveRegexOrder = debounce(errorCatched(async () => {
        const allRegexes = [...appState.regexes.global, ...appState.regexes.character];
        await TavernAPI.replaceRegexes(allRegexes.filter(r => r.source !== 'card'));
        await TavernAPI.saveSettings();
        showSuccessTick('正则顺序已保存');
    }), 800);
    // 处理正则拖拽结束事件
    const handleRegexDragEnd = errorCatched(async (evt, scope) => {
        const { oldIndex, newIndex } = evt;
        if (oldIndex === newIndex)
            return;
        const targetList = appState.regexes[scope];
        const [movedItem] = targetList.splice(oldIndex, 1);
        targetList.splice(newIndex, 0, movedItem);
        // 乐观更新UI：重新渲染序号
        renderContent();
        // 防抖保存
        debouncedSaveRegexOrder();
    });
    // --- 主程序逻辑 ---
    function main(jquery, tavernHelper) {
        $ = jquery;
        TavernHelper = tavernHelper;
        console.log('[WorldInfoOptimizer] Initializing main application...');
        // 初始化 TavernAPI
        initializeTavernAPI();
        // --- 探查代码 ---
        console.log('[WorldInfoOptimizer] Probing for event listeners...');
        console.log('[WorldInfoOptimizer] TavernHelper object:', TavernHelper);
        console.log('[WorldInfoOptimizer] SillyTavern object:', parentWin.SillyTavern);
        // --- 探查代码结束 ---
        // 加载 SortableJS 然后初始化 UI
        loadSortableJS(() => {
            console.log('[WorldInfoOptimizer] SortableJS loaded, creating UI elements...');
            // 创建主面板
            createMainPanel();
            // 创建扩展菜单按钮
            createExtensionButton();
            // 绑定事件处理器
            bindEventHandlers();
            // 加载初始数据
            loadAllData();
            console.log('[WorldInfoOptimizer] Main application initialized successfully.');
        });
    }
    // --- UI 创建函数 ---
    const createMainPanel = () => {
        const parentDoc = parentWin.document;
        // 检查面板是否已存在
        if ($(`#${PANEL_ID}`, parentDoc).length > 0) {
            console.log('[WorldInfoOptimizer] Panel already exists, skipping creation.');
            return;
        }
        const panelHtml = `
            <div id="${PANEL_ID}" class="wio-panel" style="display: none;">
                <div class="wio-panel-header">
                    <h3 class="wio-panel-title">
                        <i class="fa-solid fa-book"></i> 世界书优化器
                    </h3>
                    <div class="wio-panel-controls">
                        <button id="${REFRESH_BTN_ID}" class="wio-btn wio-btn-icon" title="刷新数据">
                            <i class="fa-solid fa-sync-alt"></i>
                        </button>
                        <button class="wio-btn wio-btn-icon wio-panel-close" title="关闭">
                            <i class="fa-solid fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="wio-panel-body">
                    <div class="wio-tabs">
                        <button class="wio-tab-btn active" data-tab="global-lore">全局世界书</button>
                        <button class="wio-tab-btn" data-tab="char-lore">角色世界书</button>
                        <button class="wio-tab-btn" data-tab="chat-lore">聊天世界书</button>
                        <button class="wio-tab-btn" data-tab="global-regex">全局正则</button>
                        <button class="wio-tab-btn" data-tab="char-regex">角色正则</button>
                    </div>
                    <div class="wio-search-section">
                        <div class="wio-search-bar">
                            <input type="text" id="${SEARCH_INPUT_ID}" placeholder="搜索世界书、条目、关键词..." class="wio-search-input">
                            <input type="text" id="wio-replace-input" placeholder="替换为..." class="wio-search-input">
                            <button id="wio-replace-btn" class="wio-btn wio-search-btn" title="替换">
                                <i class="fa-solid fa-exchange-alt"></i>
                            </button>
                        </div>
                        <div id="wio-search-filters-container" class="wio-search-filters">
                            <label><input type="checkbox" id="wio-filter-book-name" checked> 书名</label>
                            <label><input type="checkbox" id="wio-filter-entry-name" checked> 条目名</label>
                            <label><input type="checkbox" id="wio-filter-keywords" checked> 关键词</label>
                            <label><input type="checkbox" id="wio-filter-content" checked> 内容</label>
                        </div>
                    </div>
                    <div class="wio-toolbar">
                        <button id="${CREATE_LOREBOOK_BTN_ID}" class="wio-btn wio-btn-primary">
                            <i class="fa-solid fa-plus"></i> 新建世界书
                        </button>
                        <button id="${COLLAPSE_ALL_BTN_ID}" class="wio-btn">
                            <i class="fa-solid fa-compress-alt"></i> 全部折叠
                        </button>
                        <button class="wio-btn wio-multi-select-toggle">
                            <i class="fa-solid fa-check-square"></i> 多选模式
                        </button>
                        <div id="wio-multi-select-controls" class="wio-multi-select-controls" style="display: none;">
                            <div class="wio-multi-select-actions">
                                <button class="wio-multi-select-action-btn" id="wio-select-all-btn">全选</button>
                                <button class="wio-multi-select-action-btn" id="wio-select-none-btn">取消全选</button>
                                <button class="wio-multi-select-action-btn" id="wio-select-invert-btn">反选</button>
                                <button class="wio-multi-select-action-btn enable" id="wio-batch-enable-btn">批量启用</button>
                                <button class="wio-multi-select-action-btn disable" id="wio-batch-disable-btn">批量禁用</button>
                                <button class="wio-multi-select-action-btn disable" id="wio-batch-delete-btn">批量删除</button>
                                <span class="wio-selection-count" id="wio-selection-count">已选择: 0</span>
                            </div>
                        </div>
                    </div>
                    <div id="${PANEL_ID}-content" class="wio-content">
                        <p class="wio-info-text">正在初始化...</p>
                    </div>
                </div>
            </div>
        `;
        $('body', parentDoc).append(panelHtml);
        // 添加基础样式
        addBasicStyles();
    };
    const createExtensionButton = () => {
        const parentDoc = parentWin.document;
        // 检查按钮是否已存在
        if ($(`#${BUTTON_ID}`, parentDoc).length > 0) {
            console.log('[WorldInfoOptimizer] Extension button already exists, skipping creation.');
            return;
        }
        const buttonHtml = `
            <div id="${BUTTON_ID}" class="list-group-item flex-container flexGap5 interactable" title="${BUTTON_TOOLTIP}">
                <div class="fa-solid fa-book extensionsMenuExtensionButton" title="${BUTTON_TOOLTIP}"></div>
                <span>${BUTTON_TEXT_IN_MENU}</span>
            </div>
        `;
        const $extensionsMenu = $('#extensionsMenu', parentDoc);
        if ($extensionsMenu.length > 0) {
            $extensionsMenu.append(buttonHtml);
            console.log(`[WorldInfoOptimizer] Button #${BUTTON_ID} appended to #extensionsMenu.`);
        }
        else {
            console.warn('[WorldInfoOptimizer] Extensions menu not found, cannot add button.');
        }
    };
    const addBasicStyles = () => {
        const parentDoc = parentWin.document;
        // 检查样式是否已添加
        if ($('#wio-basic-styles', parentDoc).length > 0) {
            return;
        }
        const basicStyles = `
            <style id="wio-basic-styles">
                /* 面板基础样式 - 响应式改进 */
                #world-info-optimizer-panel {
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 95vw;
                    max-width: 1200px;
                    height: 85vh;
                    max-height: 800px;
                    background-color: #1a1a1a;
                    border: 2px solid #444;
                    border-radius: 8px;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.8);
                    z-index: 10000;
                    display: none;
                    flex-direction: column;
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    color: #fff;
                }

                /* 移动端适配 - 修复顶部溢出和视窗单位问题 */
                @media (max-width: 768px) {
                    #world-info-optimizer-panel {
                        /* 使用dvh单位替代vh，考虑地址栏空间 */
                        width: 98vw;
                        height: 95dvh;
                        max-height: none;
                        border-radius: 0;
                        /* 从顶部开始，避免被推出屏幕 */
                        top: 0;
                        left: 0;
                        transform: none;
                        margin: 0;
                        /* 考虑安全区域 */
                        padding-top: env(safe-area-inset-top, 0);
                        padding-bottom: env(safe-area-inset-bottom, 0);
                        padding-left: env(safe-area-inset-left, 0);
                        padding-right: env(safe-area-inset-right, 0);
                    }
                    
                    .wio-panel-header {
                        padding: 10px 15px !important;
                        /* 确保头部不被系统状态栏遮挡 */
                        padding-top: calc(10px + env(safe-area-inset-top, 0));
                    }
                    
                    .wio-toolbar {
                        padding: 10px 15px !important;
                    }
                }

                @media (max-width: 480px) {
                    #world-info-optimizer-panel {
                        width: 100vw;
                        height: 100dvh;
                        /* 完全填充，无margin */
                        top: 0;
                        left: 0;
                        transform: none;
                        margin: 0;
                        /* 全面考虑安全区域 */
                        padding-top: env(safe-area-inset-top, 0);
                        padding-bottom: env(safe-area-inset-bottom, 0);
                        padding-left: env(safe-area-inset-left, 0);
                        padding-right: env(safe-area-inset-right, 0);
                    }
                }

                /* 中等屏幕适配：768px-1200px */
                @media (max-width: 1200px) and (min-width: 769px) {
                    #world-info-optimizer-panel {
                        width: min(95vw, 1000px);
                        height: min(88vh, 750px);
                        max-width: 1000px;
                        max-height: 750px;
                        /* 新增：修复中等屏幕下的垂直居中溢出问题 */
                        top: 20px;
                        transform: translate(-50%, 0);
                    }
                    
                    .wio-toolbar {
                        gap: 8px;
                        padding: 12px 18px;
                    }
                    
                    .wio-btn {
                        padding: 6px 12px;
                        font-size: 13px;
                        white-space: nowrap;
                        flex: 0 0 auto; /* 防止拉伸 */
                    }
                }

                /* 平板适配：768px-1024px */
                @media (max-width: 1024px) and (min-width: 769px) {
                    #world-info-optimizer-panel {
                        width: 92vw;
                        height: 85vh;
                        max-width: 900px;
                        max-height: 700px;
                    }
                    
                    .wio-toolbar {
                        gap: 6px;
                        padding: 10px 15px;
                    }

                    .wio-btn {
                        padding: 5px 10px;
                        font-size: 12px;
                        flex: 0 0 auto;
                    }
                    
                    .wio-content {
                        padding: 15px;
                    }
                }

                /* 按钮基础类系统 */
                .wio-btn-base {
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    font-family: inherit;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    text-decoration: none;
                    outline: none;
                }

                /* 按钮尺寸修饰符 */
                .wio-btn-sm { padding: 4px 8px; font-size: 12px; }
                .wio-btn-md { padding: 8px 16px; font-size: 14px; }
                .wio-btn-lg { padding: 12px 24px; font-size: 16px; }

                /* 图标按钮尺寸 */
                .wio-btn-icon-sm { padding: 4px; width: 24px; height: 24px; }
                .wio-btn-icon-md { padding: 8px; width: 36px; height: 36px; }

                /* 按钮颜色主题 */
                .wio-btn-primary { background: #007bff; color: #fff; }
                .wio-btn-primary:hover { background: #0056b3; }

                .wio-btn-secondary { background: #6c757d; color: #fff; }
                .wio-btn-secondary:hover { background: #5a6268; }

                .wio-btn-success { background: #28a745; color: #fff; }
                .wio-btn-success:hover { background: #218838; }

                .wio-btn-danger { background: #dc3545; color: #fff; }
                .wio-btn-danger:hover { background: #c82333; }

                /* 统一hover效果 */
                .wio-btn-base:hover {
                    transform: translateY(-1px);
                    filter: brightness(1.05);
                }

                /* 覆盖特定按钮的hover效果 */
                .wio-btn-primary:hover,
                .wio-btn-secondary:hover,
                .wio-btn-success:hover,
                .wio-btn-danger:hover {
                    filter: none;
                    transform: translateY(-1px);
                }
                .wio-panel-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 15px 20px;
                    border-bottom: 1px solid #444;
                    background: #333;
                    border-radius: 8px 8px 0 0;
                }
                .wio-panel-title {
                    margin: 0;
                    font-size: 18px;
                    color: #fff;
                }
                .wio-panel-controls {
                    display: flex;
                    gap: 10px;
                }
                .wio-btn {
                    padding: 8px 16px;
                    border: none;
                    border-radius: 4px;
                    background: #555;
                    color: #fff;
                    cursor: pointer;
                    transition: background 0.2s;
                }
                .wio-btn:hover {
                    background: #666;
                }
                .wio-btn-primary {
                    background: #007bff;
                }
                .wio-btn-primary:hover {
                    background: #0056b3;
                }
                .wio-btn-icon {
                    padding: 8px;
                    width: 36px;
                    height: 36px;
                }
                .wio-panel-body {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                }
                .wio-tabs {
                    display: flex;
                    border-bottom: 1px solid #444;
                    background: #333;
                }
                .wio-tab-btn {
                    padding: 12px 20px;
                    border: none;
                    background: transparent;
                    color: #ccc;
                    cursor: pointer;
                    border-bottom: 2px solid transparent;
                    transition: all 0.2s;
                }
                .wio-tab-btn:hover {
                    background: #444;
                    color: #fff;
                }
                .wio-tab-btn.active {
                    color: #fff;
                    border-bottom-color: #007bff;
                    background: #444;
                }
                .wio-search-section {
                    padding: 15px 20px;
                    border-bottom: 1px solid #444;
                    background: #2a2a2a;
                }
                .wio-search-bar {
                    display: flex;
                    gap: 10px;
                    margin-bottom: 10px;
                }
                .wio-search-input {
                    flex: 1;
                    padding: 10px;
                    border: 1px solid #555;
                    border-radius: 4px;
                    background: #333;
                    color: #fff;
                }
                .wio-search-filters {
                    display: flex;
                    gap: 15px;
                    flex-wrap: wrap;
                }
                .wio-search-filters label {
                    display: flex;
                    align-items: center;
                    gap: 5px;
                    color: #ccc;
                    cursor: pointer;
                }
                .wio-toolbar {
                    padding: 15px 20px;
                    border-bottom: 1px solid #444;
                    background: #2a2a2a;
                    display: flex;
                    flex-wrap: wrap;
                    gap: 8px;
                    align-items: center;
                }
                .wio-content {
                    flex: 1;
                    padding: 20px;
                    overflow-y: auto;
                    background: #1a1a1a;
                }
                .wio-info-text {
                    text-align: center;
                    color: #888;
                    font-style: italic;
                    margin: 40px 0;
                }
                .wio-book-item {
                    margin-bottom: 20px;
                    padding: 15px;
                    background: #333;
                    border-radius: 6px;
                    border: 1px solid #444;
                }
                .wio-highlight {
                    background: #ffeb3b;
                    color: #000;
                    padding: 1px 2px;
                    border-radius: 2px;
                }
                .wio-item-container {
                    margin-bottom: 10px;
                    border: 1px solid #444;
                    border-radius: 4px;
                    background: #333;
                }
                .wio-item-container.enabled {
                    border-left: 3px solid #28a745;
                }
                .wio-item-container.selected {
                    background: #2a4a6b;
                    border-color: #007bff;
                }
                .wio-item-header {
                    display: flex;
                    align-items: center;
                    padding: 10px 15px;
                    cursor: pointer;
                    transition: background 0.2s;
                }
                .wio-item-header:hover {
                    background: #444;
                }
                .wio-item-name {
                    flex: 1;
                    margin-left: 10px;
                    font-weight: 500;
                }
                .wio-item-controls {
                    display: flex;
                    gap: 5px;
                }
                .wio-action-btn-icon {
                    padding: 4px 8px;
                    border: none;
                    border-radius: 3px;
                    background: #555;
                    color: #fff;
                    cursor: pointer;
                    font-size: 12px;
                    transition: background 0.2s;
                }
                .wio-action-btn-icon:hover {
                    background: #666;
                }
                .wio-toggle-btn {
                    padding: 4px 8px;
                    border: none;
                    border-radius: 3px;
                    background: #dc3545;
                    color: #fff;
                    cursor: pointer;
                    font-size: 12px;
                    transition: background 0.2s;
                }
                .wio-toggle-btn:hover {
                    background: #c82333;
                }
                .wio-book-group {
                    margin-bottom: 20px;
                    border: 1px solid #444;
                    border-radius: 6px;
                    background: #2a2a2a;
                }
                .wio-book-group-header, .wio-global-book-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 15px;
                    background: #333;
                    border-radius: 6px 6px 0 0;
                    cursor: pointer;
                    transition: background 0.2s;
                }
                .wio-book-group-header:hover, .wio-global-book-header:hover {
                    background: #444;
                }
                .wio-book-name {
                    font-size: 16px;
                    font-weight: 600;
                    color: #fff;
                }
                .wio-book-status {
                    margin-left: 10px;
                    padding: 2px 8px;
                    border-radius: 12px;
                    font-size: 12px;
                    font-weight: 500;
                }
                .wio-book-status.enabled {
                    background: #28a745;
                    color: #fff;
                }
                .wio-book-status.disabled {
                    background: #6c757d;
                    color: #fff;
                }
                .wio-entry-actions, .wio-regex-actions {
                    padding: 15px;
                    border-bottom: 1px solid #444;
                    display: flex;
                    gap: 10px;
                    flex-wrap: wrap;
                }
                .wio-action-btn {
                    padding: 8px 16px;
                    border: none;
                    border-radius: 4px;
                    background: #007bff;
                    color: #fff;
                    cursor: pointer;
                    font-size: 14px;
                    transition: background 0.2s;
                }
                .wio-action-btn:hover {
                    background: #0056b3;
                }

                .wio-multi-select-actions {
                    display: flex;
                    gap: 10px;
                    flex-wrap: wrap;
                    align-items: center;
                }
                .wio-multi-select-action-btn {
                    padding: 6px 12px;
                    border: none;
                    border-radius: 3px;
                    background: #6c757d;
                    color: #fff;
                    cursor: pointer;
                    font-size: 12px;
                    transition: background 0.2s;
                }
                .wio-multi-select-action-btn:hover {
                    background: #5a6268;
                }
                .wio-multi-select-action-btn.enable {
                    background: #28a745;
                }
                .wio-multi-select-action-btn.enable:hover {
                    background: #218838;
                }
                .wio-multi-select-action-btn.disable {
                    background: #dc3545;
                }
                .wio-multi-select-action-btn.disable:hover {
                    background: #c82333;
                }
                .wio-selection-count {
                    margin-left: auto;
                    color: #ccc;
                    font-size: 12px;
                }
                .wio-info-text-small {
                    text-align: center;
                    color: #888;
                    font-style: italic;
                    margin: 20px 0;
                    font-size: 14px;
                }
                .wio-used-by-chars {
                    margin-top: 5px;
                    font-size: 12px;
                    color: #aaa;
                }
                .wio-used-by-chars span {
                    background: #555;
                    padding: 2px 6px;
                    border-radius: 3px;
                    margin-right: 5px;
                }
                .wio-toast-notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #28a745;
                    color: #fff;
                    padding: 12px 20px;
                    border-radius: 6px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    z-index: 10001;
                    opacity: 0;
                    transform: translateX(100%);
                    transition: all 0.3s ease;
                }
                .wio-toast-notification.visible {
                    opacity: 1;
                    transform: translateX(0);
                }
                .wio-progress-toast {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #007bff;
                    color: #fff;
                    padding: 12px 20px;
                    border-radius: 6px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    z-index: 10001;
                    opacity: 0;
                    transform: translateX(100%);
                    transition: all 0.3s ease;
                }
                .wio-progress-toast.visible {
                    opacity: 1;
                    transform: translateX(0);
                }
                .wio-modal-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.7);
                    z-index: 10002;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    /* 移动端适配 */
                    padding: env(safe-area-inset-top, 0) env(safe-area-inset-right, 0) env(safe-area-inset-bottom, 0) env(safe-area-inset-left, 0);
                }
                .wio-modal-content {
                    background: #2a2a2a;
                    border-radius: 8px;
                    max-width: 500px;
                    width: 90%;
                    max-height: 80vh;
                    max-height: 80dvh; /* 移动端使用dvh单位 */
                    overflow-y: auto;
                    /* 移动端安全区域 */
                    margin: env(safe-area-inset-top, 0) env(safe-area-inset-right, 0) env(safe-area-inset-bottom, 0) env(safe-area-inset-left, 0);
                }

                /* 移动端模态框适配 */
                @media (max-width: 768px) {
                    .wio-modal-overlay {
                        align-items: flex-start;
                        padding-top: calc(env(safe-area-inset-top, 0) + 20px);
                    }
                    .wio-modal-content {
                        width: 95%;
                        max-height: 90dvh;
                        margin-top: env(safe-area-inset-top, 0);
                    }
                }
                .wio-modal-header {
                    padding: 20px;
                    border-bottom: 1px solid #444;
                    font-size: 18px;
                    font-weight: 600;
                    color: #fff;
                }
                .wio-modal-body {
                    padding: 20px;
                    color: #ccc;
                }
                .wio-modal-input {
                    width: 100%;
                    padding: 10px;
                    margin-top: 10px;
                    border: 1px solid #555;
                    border-radius: 4px;
                    background: #333;
                    color: #fff;
                }
                .wio-modal-input.wio-input-error {
                    border-color: #dc3545;
                    animation: shake 0.5s;
                }
                @keyframes shake {
                    0%, 100% { transform: translateX(0); }
                    25% { transform: translateX(-5px); }
                    75% { transform: translateX(5px); }
                }
                .wio-modal-footer {
                    padding: 20px;
                    border-top: 1px solid #444;
                    display: flex;
                    gap: 10px;
                    justify-content: flex-end;
                }
                .wio-modal-btn {
                    padding: 8px 16px;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                    transition: background 0.2s;
                }
                .wio-modal-ok {
                    background: #007bff;
                    color: #fff;
                }
                .wio-modal-ok:hover {
                    background: #0056b3;
                }
                .wio-modal-cancel {
                    background: #6c757d;
                    color: #fff;
                }
                .wio-modal-cancel:hover {
                    background: #5a6268;
                }
                .wio-drag-handle {
                    cursor: grab;
                    color: #ccc;
                    margin-right: 10px;
                    padding: 0 5px;
                    opacity: 0.6;
                    transition: opacity 0.2s;
                }
                .wio-drag-handle:hover {
                    opacity: 1;
                }
                .wio-drag-handle:active {
                    cursor: grabbing;
                }
                .wio-item-container.sortable-ghost {
                    opacity: 0.4;
                    background: #2a4a6b;
                }
                .wio-item-container.sortable-chosen {
                    cursor: grabbing;
                }
                .wio-order-indicator {
                    display: inline-block;
                    background: #007bff;
                    color: #fff;
                    font-size: 10px;
                    font-weight: bold;
                    padding: 2px 6px;
                    border-radius: 10px;
                    margin-right: 8px;
                    min-width: 20px;
                    text-align: center;
                }
                .wio-regex-list {
                    padding: 15px;
                }
                /* 按钮激活状态 */
                #${BUTTON_ID}.active {
                    background-color: rgba(126, 183, 213, 0.3) !important;
                    border-color: #7eb7d5 !important;
                }
                #${BUTTON_ID}:hover {
                    background-color: rgba(126, 183, 213, 0.15);
                }
                /* 重命名UI样式 */
                .wio-rename-ui {
                    display: flex;
                    align-items: center;
                    flex-grow: 1;
                    margin-left: 10px;
                }
                .wio-rename-input-wrapper {
                    display: flex;
                    align-items: center;
                    flex-grow: 1;
                    background-color: #2a2a2a;
                    border-radius: 6px;
                    padding: 4px;
                    border: 2px solid #007bff;
                    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
                }
                .wio-rename-input {
                    flex-grow: 1;
                    border: none;
                    background: transparent;
                    color: #fff;
                    font-size: 14px;
                    font-weight: 600;
                    padding: 6px 8px;
                    outline: none;
                    min-width: 200px;
                }
                .wio-rename-input::placeholder {
                    color: #888;
                }
                .wio-rename-buttons {
                    display: flex;
                    gap: 4px;
                    margin-left: 8px;
                }
                .wio-rename-ui .wio-action-btn-icon {
                    width: 28px;
                    height: 28px;
                    border-radius: 4px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    transition: all 0.2s;
                    font-size: 12px;
                }
                .wio-rename-save-btn {
                    background-color: #28a745;
                    color: white;
                }
                .wio-rename-save-btn:hover {
                    background-color: #218838;
                }
                .wio-rename-cancel-btn {
                    background-color: #dc3545;
                    color: white;
                }
                .wio-rename-cancel-btn:hover {
                    background-color: #c82333;
                }
                .wio-item-container.renaming .wio-item-header {
                    background-color: #2a4a6b;
                }
                /* 编辑器样式 */
                .wio-collapsible-content {
                    display: none;
                    max-height: 45vh;
                    max-height: 45dvh; /* 移动端使用dvh单位 */
                    overflow-y: auto;
                }
                .wio-book-group .wio-collapsible-content {
                    max-height: none;
                    overflow: visible;
                    padding-top: 10px;
                    border-top: 1px solid #444;
                }
                .wio-editor-wrapper {
                    padding: 15px;
                    display: flex;
                    flex-direction: column;
                    background-color: #2a2a2a;
                    border-radius: 6px;
                    margin: 10px;
                }
                .wio-editor-field {
                    margin-top: 15px;
                    display: flex;
                    flex-direction: column;
                }
                .wio-editor-field:first-child {
                    margin-top: 0;
                }
                .wio-editor-field label {
                    font-weight: 600;
                    margin-bottom: 5px;
                    color: #ccc;
                }
                .wio-editor-field input[type=text],
                .wio-editor-field textarea,
                .wio-editor-field select,
                .wio-editor-field .wio-edit-keys[contenteditable="true"] {
                    width: 100%;
                    min-height: 35px;
                    padding: 8px;
                    border-radius: 6px;
                    border: 1px solid #555;
                    background-color: #1a1a1a;
                    color: #fff;
                    font-family: inherit;
                }
                .wio-editor-field textarea {
                    min-height: 80px;
                    resize: vertical;
                }
                .wio-edit-content {
                    min-height: 80px;
                    padding: 8px;
                    border-radius: 6px;
                    border: 1px solid #555;
                    background-color: #1a1a1a;
                    color: #fff;
                    outline: none;
                    white-space: pre-wrap; /* Linus's Fix: Let CSS handle newlines */
                }
                .wio-editor-group {
                    margin-top: 20px;
                }
                .wio-editor-group h5 {
                    margin: 0 0 10px 0;
                    color: #007bff;
                    font-size: 14px;
                    font-weight: 600;
                }
                .wio-editor-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr 1fr;
                    gap: 10px;
                }
                .wio-grid-item {
                    display: flex;
                    flex-direction: column;
                }
                .wio-editor-options-row {
                    display: flex;
                    gap: 15px;
                    flex-wrap: wrap;
                }
                .wio-editor-option-item {
                    display: flex;
                    align-items: center;
                    gap: 5px;
                    color: #ccc;
                    cursor: pointer;
                }
                .wio-editor-actions {
                    margin-top: 20px;
                    display: flex;
                    gap: 10px;
                    justify-content: flex-end;
                }
                .wio-save-btn {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-weight: 600;
                }
                .wio-save-btn:hover {
                    background-color: #218838;
                }
                .wio-cancel-btn {
                    background-color: #6c757d;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-weight: 600;
                }
                .wio-cancel-btn:hover {
                    background-color: #5a6268;
                }
                /* 多选模式样式 */
                .wio-multi-select-mode .wio-item-container,
                .wio-multi-select-mode .wio-book-group,
                .wio-book-group.editing-entries .wio-item-container {
                    position: relative;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }
                .wio-multi-select-mode .wio-item-container:hover,
                .wio-multi-select-mode .wio-book-group:hover,
                .wio-book-group.editing-entries .wio-item-container:hover {
                    background-color: rgba(0, 123, 255, 0.1);
                }
                .wio-multi-select-mode .wio-item-container.selected,
                .wio-multi-select-mode .wio-book-group.selected,
                .wio-book-group.editing-entries .wio-item-container.selected {
                    background-color: rgba(0, 123, 255, 0.2) !important;
                    border-left: 4px solid #007bff;
                    box-shadow: inset 0 0 0 1px rgba(0, 123, 255, 0.3);
                }
                .wio-multi-select-mode .wio-item-container.selected::before,
                .wio-multi-select-mode .wio-book-group.selected::before,
                .wio-book-group.editing-entries .wio-item-container.selected::before {
                    content: "✓";
                    position: absolute;
                    left: 8px;
                    top: 50%;
                    transform: translateY(-50%);
                    color: #007bff;
                    font-weight: bold;
                    font-size: 16px;
                    z-index: 10;
                }
                .wio-multi-select-mode .wio-item-header,
                .wio-multi-select-mode .wio-global-book-header,
                .wio-book-group.editing-entries .wio-item-header {
                    padding-left: 35px;
                }
                /* 多选控制面板 - 美化 */
                #wio-multi-select-controls {
                    background-color: #3a3a3a; /* 更柔和的背景 */
                    border: 1px solid #4f4f4f; /* 添加边框 */
                    border-radius: 6px; /* 圆角 */
                    padding: 8px 12px; /* 调整内边距 */
                    margin-left: 10px; /* 与主按钮的间距 */
                    box-shadow: inset 0 1px 2px rgba(0,0,0,0.2); /* 内阴影增加深度 */
                    flex-wrap: wrap;
                    gap: 8px;
                    align-items: center;
                }
                #wio-multi-select-controls .wio-selection-count {
                    color: #ccc; /* 中性颜色 */
                    font-weight: 600;
                    margin-right: 15px;
                }
                #wio-multi-select-controls .wio-batch-btn {
                    background-color: #495057;
                    color: white;
                    border: none;
                    padding: 6px 12px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                    transition: background-color 0.2s;
                }
                #wio-multi-select-controls .wio-batch-btn:hover {
                    background-color: #6c757d;
                }
                #wio-multi-select-controls .wio-batch-btn.danger {
                    background-color: #dc3545;
                }
                #wio-multi-select-controls .wio-batch-btn.danger:hover {
                    background-color: #c82333;
                }
                #wio-multi-select-controls .wio-batch-btn.success {
                    background-color: #28a745;
                }
                #wio-multi-select-controls .wio-batch-btn.success:hover {
                    background-color: #218838;
                }
                .wio-loading-container {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    height: 100%;
                    padding: 20px;
                    text-align: center;
                }
                .wio-loading-title {
                    font-size: 18px;
                    font-weight: 600;
                    margin-bottom: 20px;
                    color: #fff;
                }
                .wio-loading-progress-bar-container {
                    width: 80%;
                    max-width: 400px;
                    height: 10px;
                    background-color: #444;
                    border-radius: 5px;
                    overflow: hidden;
                    margin-bottom: 15px;
                }
                .wio-loading-progress-bar {
                    height: 100%;
                    background-color: #007bff;
                    border-radius: 5px;
                    transition: width 0.3s ease;
                }
                .wio-loading-status-text {
                    font-size: 14px;
                    color: #ccc;
                }
            </style>
        `;
        $('head', parentDoc).append(basicStyles);
    };
    // --- 面板显示/隐藏函数 ---
    const hidePanel = () => {
        const parentDoc = parentWin.document;
        const $panel = $(`#${PANEL_ID}`, parentDoc);
        const $parentBody = $('body', parentDoc);
        $panel.hide();
        $(`#${BUTTON_ID}`, parentDoc).removeClass('active');
        $parentBody.off('mousedown.wio-outside-click');
    };
    const showPanel = async () => {
        const parentDoc = parentWin.document;
        const $panel = $(`#${PANEL_ID}`, parentDoc);
        const $parentBody = $('body', parentDoc);
        $panel.css('display', 'flex');
        $(`#${BUTTON_ID}`, parentDoc).addClass('active');
        // 点击外部关闭面板
        $parentBody.on('mousedown.wio-outside-click', function (event) {
            if ($(event.target).closest(`#${PANEL_ID}`).length === 0 &&
                $(event.target).closest(`#${BUTTON_ID}`).length === 0) {
                hidePanel();
            }
        });
        await loadAllData();
    };
    // --- 按钮事件处理函数 ---
    const handleHeaderClick = errorCatched(async (event) => {
        const $target = $(event.target);
        const $container = $(event.currentTarget).closest('.wio-item-container, .wio-book-group');
        // 如果点击的是按钮等可交互控件，则不执行后续逻辑
        if ($target.closest('.wio-item-controls, .wio-rename-ui').length > 0) {
            return;
        }
        // 如果处于全局多选模式，或者局部条目编辑模式
        const isBookGroupEditing = $container.closest('.wio-book-group').hasClass('editing-entries');
        if (appState.multiSelectMode || isBookGroupEditing) {
            let itemKey;
            const isGlobalLoreTab = appState.activeTab === 'global-lore';
            const isBookHeader = $container.hasClass('wio-book-group');
            // 全局多选模式下，且非局部编辑模式时，才可以选择 book header
            if (appState.multiSelectMode && isGlobalLoreTab && isBookHeader && !isBookGroupEditing) {
                const bookName = $container.data('book-name');
                itemKey = `book:${bookName}`;
            }
            // 任何多选模式下都可以选择 item
            else if ($container.hasClass('wio-item-container')) {
                const itemType = $container.data('type');
                const itemId = $container.data('id');
                if (itemType === 'lore') {
                    const bookName = $container.data('book-name');
                    itemKey = `lore:${bookName}:${itemId}`;
                }
                else if (itemType === 'regex' && appState.multiSelectMode) {
                    // 正则只能在全局多选下选择
                    itemKey = `regex:${itemId}`;
                }
            }
            if (itemKey) {
                if (appState.selectedItems.has(itemKey)) {
                    appState.selectedItems.delete(itemKey);
                    $container.removeClass('selected');
                }
                else {
                    appState.selectedItems.add(itemKey);
                    $container.addClass('selected');
                }
                updateSelectionCount();
            }
            // 如果不是全局多选模式（即仅局部编辑），则不继续执行折叠逻辑
            if (!appState.multiSelectMode) {
                return;
            }
        }
        // 非多选模式：展开/折叠内容
        if ($container.hasClass('from-card') || $container.hasClass('renaming'))
            return;
        const $content = $container.find('.wio-collapsible-content').first();
        // 对于世界书组，总是展开/折叠
        if ($container.is('.wio-book-group')) {
            $content.slideToggle(200);
            return;
        }
        // 如果内容已经可见，则折叠
        if ($content.is(':visible')) {
            $content.slideUp(200, () => $content.empty());
            return;
        }
        // 折叠其他同级项目
        $container.siblings('.wio-item-container').find('.wio-collapsible-content:visible').slideUp(200).empty();
        // 展开当前项目并加载内容
        const type = $container.data('type');
        const id = $container.data('id');
        let item, editorHtml;
        if (type === 'lore') {
            const bookName = $container.data('book-name');
            const entries = [...safeGetLorebookEntries(bookName)];
            item = entries.find((e) => e.uid === id);
            if (!item)
                return;
            // 获取搜索词以用于内容高亮
            const searchTerm = $container.data('searchTerm') || '';
            // 直接使用原始内容，不需要额外的unescapeHtml
            const highlightedContent = highlightText(item.content || '', searchTerm);
            const keywordsText = (item.keys || []).join(', ');
            // 直接使用原始关键词，不需要额外的unescapeHtml
            const highlightedKeywords = highlightText(keywordsText, searchTerm);
            const positionOptions = Object.entries(LOREBOOK_OPTIONS.position)
                .map(([value, text]) => `<option value="${value}" ${item.position === value ? 'selected' : ''}>${text}</option>`)
                .join('');
            const logicOptions = Object.entries(LOREBOOK_OPTIONS.logic)
                .map(([value, text]) => `<option value="${value}" ${item.logic === value ? 'selected' : ''}>${text}</option>`)
                .join('');
            editorHtml = `
        <div class="wio-editor-wrapper">
          <div class="wio-editor-field">
            <label>关键词 (逗号分隔)</label>
            <div class="wio-edit-keys" contenteditable="true">${highlightedKeywords}</div>
          </div>
          <div class="wio-editor-field">
            <label>内容</label>
            <div class="wio-edit-content" contenteditable="true">${highlightedContent}</div>
          </div>
          <div class="wio-editor-group">
            <h5>插入规则</h5>
            <div class="wio-editor-grid">
              <div class="wio-grid-item">
                <label>位置</label>
                <select class="wio-edit-position">${positionOptions}</select>
              </div>
              <div class="wio-grid-item">
                <label>深度</label>
                <input type="number" class="wio-edit-depth" placeholder="例如: 0" value="${item.depth ?? ''}">
              </div>
              <div class="wio-grid-item">
                <label>顺序</label>
                <input type="number" class="wio-edit-order" placeholder="例如: 100" value="${item.order ?? ''}">
              </div>
            </div>
          </div>
          <div class="wio-editor-group">
            <h5>激活逻辑</h5>
            <div class="wio-editor-grid">
              <div class="wio-grid-item">
                <label>概率 (%)</label>
                <input type="number" class="wio-edit-probability" min="0" max="100" placeholder="100" value="${item.probability ?? ''}">
              </div>
              <div class="wio-grid-item">
                <label>关键词逻辑</label>
                <select class="wio-edit-logic">${logicOptions}</select>
              </div>
            </div>
          </div>
          <div class="wio-editor-group">
            <h5>匹配与递归</h5>
            <div class="wio-editor-options-row">
              <label class="wio-editor-option-item">
                <input type="checkbox" class="wio-edit-case-sensitive" ${item.case_sensitive ? 'checked' : ''}> 大小写敏感
              </label>
              <label class="wio-editor-option-item">
                <input type="checkbox" class="wio-edit-match-whole" ${item.match_whole_words ? 'checked' : ''}> 全词匹配
              </label>
              <label class="wio-editor-option-item">
                <input type="checkbox" class="wio-edit-prevent-recursion" ${item.prevent_recursion ? 'checked' : ''}> 防止递归
              </label>
            </div>
          </div>
          <div class="wio-editor-actions">
            <button class="wio-save-btn">保存更改</button>
            <button class="wio-cancel-btn">取消</button>
          </div>
        </div>
      `;
        }
        else {
            // Regex editor
            item = [...appState.regexes.global, ...appState.regexes.character].find((r) => r.id === id);
            if (!item)
                return;
            editorHtml = `
        <div class="wio-editor-wrapper">
          <div class="wio-editor-field">
            <label>查找正则表达式</label>
            <textarea class="wio-edit-find">${escapeHtml(item.find_regex || '')}</textarea>
          </div>
          <div class="wio-editor-field">
            <label>替换为</label>
            <textarea class="wio-edit-replace">${escapeHtml(item.replace_string || '')}</textarea>
          </div>
          <div class="wio-editor-actions">
            <button class="wio-save-btn">保存更改</button>
            <button class="wio-cancel-btn">取消</button>
          </div>
        </div>
      `;
        }
        $content.html(editorHtml).slideDown(200);
    });
    const handleToggleState = errorCatched(async (event) => {
        event.stopPropagation();
        const $button = $(event.currentTarget);
        const $elementToSort = $button.closest('.wio-book-group, .wio-item-container');
        if ($elementToSort.hasClass('renaming'))
            return;
        const isEnabling = !$elementToSort.hasClass('enabled');
        const parentList = $elementToSort.parent();
        if ($button.hasClass('wio-global-toggle')) {
            const bookName = $elementToSort.data('book-name');
            const settings = await TavernAPI.getLorebookSettings();
            const currentBooks = new Set(settings.selected_global_lorebooks || []);
            if (isEnabling)
                currentBooks.add(bookName);
            else
                currentBooks.delete(bookName);
            await TavernAPI.setLorebookSettings({ selected_global_lorebooks: Array.from(currentBooks) });
            await TavernAPI.saveSettings();
            const bookState = appState.allLorebooks.find(b => b.name === bookName);
            if (bookState)
                bookState.enabled = isEnabling;
        }
        else {
            const type = $elementToSort.data('type');
            const id = $elementToSort.data('id');
            if (type === 'lore') {
                const bookName = $elementToSort.data('book-name');
                await TavernAPI.setLorebookEntries(bookName, [{ uid: Number(id), enabled: isEnabling }]);
                const entry = safeGetLorebookEntries(bookName).find((e) => e.uid === Number(id));
                if (entry)
                    entry.enabled = isEnabling;
            }
            else {
                const allServerRegexes = await TavernAPI.getRegexes();
                const regex = allServerRegexes.find((r) => r.id === id);
                if (regex) {
                    regex.enabled = isEnabling;
                    await TavernAPI.replaceRegexes(allServerRegexes.filter((r) => r.source !== 'card'));
                    await TavernAPI.saveSettings();
                    const localRegex = appState.regexes.global.find((r) => r.id === id) ||
                        appState.regexes.character.find((r) => r.id === id);
                    if (localRegex)
                        localRegex.enabled = isEnabling;
                }
            }
        }
        showSuccessTick(isEnabling ? '已启用' : '已禁用');
        $elementToSort.toggleClass('enabled', isEnabling);
        // 重新排序：启用的项目排在前面
        const items = parentList.children().get();
        items.sort((a, b) => {
            const aEnabled = $(a).hasClass('enabled');
            const bEnabled = $(b).hasClass('enabled');
            if (aEnabled !== bEnabled)
                return bEnabled ? 1 : -1;
            const aName = $(a).find('.wio-item-name').text().trim();
            const bName = $(b).find('.wio-item-name').text().trim();
            return aName.localeCompare(bName);
        });
        parentList.append(items);
    });
    const handleRename = errorCatched(async (event) => {
        event.stopPropagation();
        const $container = $(event.currentTarget).closest('.wio-item-container');
        if ($container.hasClass('renaming') || $container.length === 0)
            return;
        const $header = $container.find('.wio-item-header').first();
        const $nameSpan = $header.find('.wio-item-name').first();
        // 获取纯文本名称，排除子元素（如序号指示器）
        let oldName = $nameSpan.text().trim();
        // 如果有序号指示器，去掉它
        const orderMatch = oldName.match(/^#\d+\s+(.+)$/);
        if (orderMatch) {
            oldName = orderMatch[1];
        }
        // 隐藏原始内容，显示编辑UI
        $header.find('.wio-item-name, .wio-item-controls').hide();
        const renameUIHtml = `
      <div class="wio-rename-ui">
        <div class="wio-rename-input-wrapper">
          <input type="text" class="wio-rename-input" value="${escapeHtml(oldName)}" />
          <div class="wio-rename-buttons">
            <button class="wio-action-btn-icon wio-rename-save-btn" title="确认">
              <i class="fa-solid fa-check"></i>
            </button>
            <button class="wio-action-btn-icon wio-rename-cancel-btn" title="取消">
              <i class="fa-solid fa-times"></i>
            </button>
          </div>
        </div>
      </div>
    `;
        $container.addClass('renaming');
        $header.append(renameUIHtml);
        const $input = $header.find('.wio-rename-input');
        $input.focus().select();
        // 自动调整输入框宽度
        $input.css('width', Math.max(200, oldName.length * 8 + 80) + 'px');
    });
    const exitRenameMode = ($container, newName = null) => {
        const $header = $container.find('.wio-item-header').first();
        const $nameSpan = $header.find('.wio-item-name').first();
        if (newName) {
            // 保留序号指示器，只更新名称部分
            const $orderIndicator = $nameSpan.find('.wio-order-indicator');
            if ($orderIndicator.length > 0) {
                $nameSpan.html($orderIndicator[0].outerHTML + ' ' + escapeHtml(newName));
            }
            else {
                $nameSpan.text(newName);
            }
        }
        // 移除编辑UI，恢复原始控件
        $header.find('.wio-rename-ui').remove();
        $header.find('.wio-item-name, .wio-item-controls').show();
        $container.removeClass('renaming');
    };
    const handleConfirmRename = errorCatched(async (event) => {
        event.stopPropagation();
        const $container = $(event.currentTarget).closest('.wio-item-container');
        const $input = $container.find('.wio-rename-input');
        const newName = $input.val().trim();
        const oldName = $container.find('.wio-item-name').first().text().trim();
        if (!newName || newName === oldName) {
            exitRenameMode($container, oldName);
            return;
        }
        const type = $container.data('type');
        const id = $container.data('id');
        if (type === 'lore') {
            const bookName = $container.data('book-name');
            await TavernAPI.setLorebookEntries(bookName, [{ uid: Number(id), comment: newName }]);
            const entry = safeGetLorebookEntries(bookName).find((e) => e.uid === Number(id));
            if (entry)
                entry.comment = newName;
        }
        else {
            // type === 'regex'
            const allServerRegexes = await TavernAPI.getRegexes();
            const regex = allServerRegexes.find((r) => r.id === id);
            if (regex) {
                regex.script_name = newName;
                await TavernAPI.replaceRegexes(allServerRegexes.filter((r) => r.source !== 'card'));
                await TavernAPI.saveSettings();
                const localRegex = appState.regexes.global.find((r) => r.id === id) ||
                    appState.regexes.character.find((r) => r.id === id);
                if (localRegex)
                    localRegex.script_name = newName;
            }
        }
        exitRenameMode($container, newName);
        showSuccessTick('重命名成功');
    });
    const handleCancelRename = errorCatched(async (event) => {
        event.stopPropagation();
        const $container = $(event.currentTarget).closest('.wio-item-container');
        exitRenameMode($container);
    });
    const handleRenameKeydown = errorCatched(async (event) => {
        if (event.key === 'Enter') {
            $(event.currentTarget).siblings('.wio-rename-save-btn').click();
        }
        else if (event.key === 'Escape') {
            $(event.currentTarget).siblings('.wio-rename-cancel-btn').click();
        }
    });
    const handleDeleteEntry = errorCatched(async (event) => {
        event.stopPropagation();
        const $item = $(event.currentTarget).closest('.wio-item-container');
        const bookName = $item.data('book-name');
        const uid = Number($item.data('id'));
        const entryName = $item.find('.wio-item-name').text().trim();
        const confirmed = await showModal({
            type: 'confirm',
            title: '确认删除',
            text: `确定要删除条目"${entryName}"吗？此操作不可撤销。`,
        });
        if (confirmed) {
            await TavernAPI.deleteLorebookEntries(bookName, [uid.toString()]);
            const entries = safeGetLorebookEntries(bookName);
            const index = entries.findIndex((e) => e.uid === uid);
            if (index !== -1)
                entries.splice(index, 1);
            $item.remove();
            showSuccessTick('条目已删除');
        }
    });
    const handleDeleteBook = errorCatched(async (event) => {
        event.stopPropagation();
        const $bookGroup = $(event.currentTarget).closest('.wio-book-group');
        const bookName = $bookGroup.data('book-name');
        const confirmed = await showModal({
            type: 'confirm',
            title: '确认删除',
            text: `确定要删除世界书"${bookName}"吗？此操作不可撤销。`,
        });
        if (confirmed) {
            await TavernAPI.deleteLorebook(bookName);
            const index = appState.allLorebooks.findIndex(b => b.name === bookName);
            if (index !== -1)
                appState.allLorebooks.splice(index, 1);
            appState.lorebookEntries.delete(bookName);
            $bookGroup.remove();
            showSuccessTick('世界书已删除');
        }
    });
    const updateLinkedCharactersAndSettings = errorCatched(async (oldName, newName, progressToast) => {
        const context = parentWin.SillyTavern.getContext() || {};
        const allCharacters = Array.isArray(context.characters) ? context.characters : [];
        const originalCharId = context.characterId;
        progressToast.update('正在更新全局设置...');
        const settings = await TavernAPI.getLorebookSettings();
        const globalBooks = new Set(settings.selected_global_lorebooks || []);
        if (globalBooks.has(oldName)) {
            globalBooks.delete(oldName);
            globalBooks.add(newName);
            await TavernAPI.setLorebookSettings({ selected_global_lorebooks: Array.from(globalBooks) });
        }
        if (appState.chatLorebook === oldName) {
            progressToast.update('正在更新当前聊天设置...');
            await TavernAPI.setChatLorebook(newName);
            appState.chatLorebook = newName;
        }
        const linkedChars = appState.lorebookUsage.get(oldName) || [];
        if (linkedChars.length > 0) {
            for (let i = 0; i < linkedChars.length; i++) {
                const charName = linkedChars[i];
                const char = allCharacters.find((c) => c.name === charName);
                if (!char)
                    continue;
                progressToast.update(`正在更新角色: ${charName} (${i + 1}/${linkedChars.length})`);
                // 切换到目标角色以修改其数据
                await context.selectCharacterById(char.avatar);
                const charBooks = await TavernAPI.getCurrentCharLorebooks();
                let updated = false;
                if (charBooks.primary === oldName) {
                    charBooks.primary = newName;
                    updated = true;
                }
                if (charBooks.additional && charBooks.additional.includes(oldName)) {
                    charBooks.additional = charBooks.additional.map((book) => (book === oldName ? newName : book));
                    updated = true;
                }
                if (updated) {
                    await TavernAPI.setCurrentCharLorebooks(charBooks);
                }
            }
        }
        // 切换回原始角色
        if (originalCharId !== undefined && context.characterId !== originalCharId) {
            progressToast.update('正在恢复原始角色...');
            await context.selectCharacterById(originalCharId);
        }
        // 更新本地的 aopState.lorebookUsage 缓存
        if (appState.lorebookUsage.has(oldName)) {
            const chars = appState.lorebookUsage.get(oldName);
            appState.lorebookUsage.delete(oldName);
            appState.lorebookUsage.set(newName, chars);
        }
    });
    const handleRenameBook = errorCatched(async (event) => {
        event.stopPropagation();
        const $bookGroup = $(event.currentTarget).closest('.wio-book-group');
        const oldName = $bookGroup.data('book-name');
        if (!oldName)
            return;
        const newName = await showModal({
            type: 'prompt',
            title: '重命名世界书',
            text: '请输入新的世界书名称：',
            value: oldName,
        });
        if (newName && newName.trim() && newName.trim() !== oldName) {
            const finalNewName = newName.trim();
            const linkedChars = appState.lorebookUsage.get(oldName) || [];
            let confirmText = `确定要将世界书 "${oldName}" 重命名为 "${finalNewName}" 吗？`;
            if (linkedChars.length > 0) {
                confirmText += `\n\n警告：此操作将会临时切换 ${linkedChars.length} 个关联角色的界面来更新其设置，过程可能会有视觉上的闪烁。请在操作完成前不要进行其他操作。`;
            }
            const confirmed = await showModal({
                type: 'confirm',
                title: '确认重命名',
                text: confirmText,
            });
            if (!confirmed)
                return;
            const progressToast = showProgressToast('正在开始重命名...');
            try {
                progressToast.update('步骤 1/4: 创建新的世界书...');
                await TavernAPI.createLorebook(finalNewName);
                const entries = safeGetLorebookEntries(oldName);
                if (entries.length > 0) {
                    progressToast.update('步骤 2/4: 复制条目...');
                    await TavernAPI.createLorebookEntries(finalNewName, entries);
                }
                safeSetLorebookEntries(finalNewName, entries);
                progressToast.update('步骤 3/4: 更新关联的角色与设置...');
                await updateLinkedCharactersAndSettings(oldName, finalNewName, progressToast);
                progressToast.update('步骤 4/4: 删除旧的世界书...');
                await TavernAPI.deleteLorebook(oldName);
                // 更新本地状态
                const bookState = appState.allLorebooks.find(b => b.name === oldName);
                if (bookState) {
                    bookState.name = finalNewName;
                }
                safeDeleteLorebookEntries(oldName);
                progressToast.remove();
                showSuccessTick('世界书重命名成功');
                await loadAllData(); // 使用loadAllData以保证状态完全同步
            }
            catch (error) {
                progressToast.remove();
                console.error('[WorldInfoOptimizer] Rename book error:', error);
                await showModal({
                    type: 'alert',
                    title: '重命名失败',
                    text: '操作过程中发生错误，部分更改可能未生效。建议刷新数据。请检查开发者控制台获取详细信息。',
                });
                await loadAllData();
            }
        }
    });
    const handleCreateEntry = errorCatched(async (event) => {
        const bookName = $(event.currentTarget).data('book-name');
        const entryName = await showModal({
            type: 'prompt',
            title: '新建条目',
            text: '请输入条目名称：',
            value: '新条目',
        });
        if (entryName) {
            const newEntry = {
                uid: Date.now().toString(),
                comment: entryName,
                keys: [entryName],
                content: '',
                enabled: true,
                insertion_order: 100,
                case_sensitive: false,
                name: entryName,
                priority: 400,
                id: Date.now(),
                key: [entryName],
                keysecondary: [],
                selective: true,
                constant: false,
                vectorized: false,
                selectiveLogic: 0,
                addMemo: false,
                order: 100,
                position: 0,
                disable: false,
                excludeRecursion: false,
                delayUntilRecursion: false,
                display_index: 0,
                forceActivation: false,
                automationId: '',
                role: 0,
                scanDepth: null,
                caseSensitive: false,
                matchWholeWords: false,
                useGroupScoring: false,
                groupOverride: false,
                groupWeight: 100,
                sticky: 0,
                cooldown: 0,
                delay: 0,
            };
            await TavernAPI.createLorebookEntries(bookName, [newEntry]);
            const entries = safeGetLorebookEntries(bookName);
            entries.push(newEntry);
            showSuccessTick('条目创建成功');
            renderContent();
        }
    });
    const handleBatchSetRecursion = errorCatched(async (event) => {
        const bookName = $(event.currentTarget).data('book-name');
        const entries = [...safeGetLorebookEntries(bookName)];
        if (!entries || entries.length === 0) {
            await showModal({ type: 'alert', title: '提示', text: '该世界书没有条目可操作。' });
            return;
        }
        const confirmed = await showModal({
            type: 'confirm',
            title: '批量设置防递归',
            text: `确定要为"${bookName}"中的所有 ${entries.length} 个条目开启"防止递归"吗？`,
        });
        if (confirmed) {
            const updatedEntries = entries.map((entry) => ({
                uid: entry.uid,
                excludeRecursion: true,
            }));
            await TavernAPI.setLorebookEntries(bookName, updatedEntries);
            // 更新本地状态
            entries.forEach((entry) => {
                entry.excludeRecursion = true;
            });
            showSuccessTick('已为所有条目开启"防止递归"');
        }
    });
    const handleFixKeywords = errorCatched(async (event) => {
        const bookName = $(event.currentTarget).data('book-name');
        const entries = [...safeGetLorebookEntries(bookName)];
        if (!entries || entries.length === 0) {
            await showModal({ type: 'alert', title: '提示', text: '该世界书没有条目可操作。' });
            return;
        }
        const confirmed = await showModal({
            type: 'confirm',
            title: '修复关键词',
            text: `确定要修复"${bookName}"中所有条目的关键词格式吗？这将清理重复和空白关键词。`,
        });
        if (confirmed) {
            let fixedCount = 0;
            const updatedEntries = entries
                .map((entry) => {
                const originalKeys = entry.keys || [];
                const cleanedKeys = [
                    ...new Set(originalKeys.map((key) => key.trim()).filter((key) => key.length > 0)),
                ];
                if (JSON.stringify(originalKeys) !== JSON.stringify(cleanedKeys)) {
                    fixedCount++;
                    return {
                        uid: entry.uid,
                        keys: cleanedKeys,
                    };
                }
                return null;
            })
                .filter(Boolean);
            if (updatedEntries.length > 0) {
                await TavernAPI.setLorebookEntries(bookName, updatedEntries);
                // 更新本地状态
                entries.forEach((entry) => {
                    const update = updatedEntries.find((u) => u.uid === entry.uid);
                    if (update) {
                        entry.keys = update.keys;
                    }
                });
            }
            showSuccessTick(`已修复 ${fixedCount} 个条目的关键词`);
        }
    });
    const handleEditEntriesToggle = errorCatched(async (event) => {
        event.stopPropagation();
        const $button = $(event.currentTarget);
        const $bookGroup = $button.closest('.wio-book-group');
        const isEditing = $bookGroup.hasClass('editing-entries');
        if (isEditing) {
            $bookGroup.removeClass('editing-entries');
            $button.attr('title', '多选条目').find('i').removeClass('fa-check-square').addClass('fa-list-check');
            $button.removeClass('active');
        }
        else {
            $bookGroup.addClass('editing-entries');
            $button.attr('title', '完成选择').find('i').removeClass('fa-list-check').addClass('fa-check-square');
            $button.addClass('active');
            // 主动打开条目列表
            $bookGroup.find('.wio-collapsible-content').slideDown(200);
        }
        // 更新多选控件的可见性
        const isAnyEditing = appState.multiSelectMode || $('.wio-book-group.editing-entries', parentWin.document).length > 0;
        $(`#wio-multi-select-controls`, parentWin.document).toggle(isAnyEditing);
    });
    const handleCollapseAll = errorCatched(async () => {
        const parentDoc = parentWin.document;
        const $allCollapsible = $(`#${PANEL_ID} .wio-collapsible-content`, parentDoc);
        $allCollapsible.slideUp(200);
        $allCollapsible.closest('.wio-item-container, .wio-book-group').addClass('collapsed');
        showSuccessTick('已折叠所有项目');
    });
    const handleCollapseCurrent = errorCatched(async () => {
        const parentDoc = parentWin.document;
        const activeTab = appState.activeTab;
        const $currentTabContent = $(`#${PANEL_ID} .wio-tab-content[data-tab="${activeTab}"] .wio-collapsible-content`, parentDoc);
        $currentTabContent.slideUp(200);
        $currentTabContent.closest('.wio-item-container, .wio-book-group').addClass('collapsed');
        showSuccessTick('已折叠当前标签页的所有项目');
    });
    const handleSaveEditor = errorCatched(async (event) => {
        event.stopPropagation();
        const $editor = $(event.currentTarget).closest('.wio-editor-wrapper');
        const $container = $editor.closest('.wio-item-container');
        const type = $container.data('type');
        const id = $container.data('id');
        if (type === 'lore') {
            const bookName = $container.data('book-name');
            const entries = [...safeGetLorebookEntries(bookName)];
            const entry = entries.find((e) => e.uid === id);
            if (!entry)
                return;
            // 收集编辑器中的数据
            const updatedData = {
                uid: entry.uid,
                keys: $editor
                    .find('.wio-edit-keys')
                    .text() // 使用 .text() 从 contenteditable div 获取纯文本
                    .toString()
                    .split(',')
                    .map((k) => k.trim())
                    .filter((k) => k),
                content: $editor.find('.wio-edit-content').text() || '',
                position: parseInt($editor.find('.wio-edit-position').val().toString()) || 0,
                depth: parseInt($editor.find('.wio-edit-depth').val().toString()) || 0,
                order: parseInt($editor.find('.wio-edit-order').val().toString()) || 100,
                probability: parseInt($editor.find('.wio-edit-probability').val().toString()) || 100,
                logic: parseInt($editor.find('.wio-edit-logic').val().toString()) || 0,
                case_sensitive: $editor.find('.wio-edit-case-sensitive').is(':checked'),
                match_whole_words: $editor.find('.wio-edit-match-whole').is(':checked'),
                prevent_recursion: $editor.find('.wio-edit-prevent-recursion').is(':checked'),
            };
            // 保存到服务器
            await TavernAPI.setLorebookEntries(bookName, [updatedData]);
            // 更新本地状态
            Object.assign(entry, updatedData);
            showSuccessTick('条目已保存');
        }
        else {
            // Regex editor
            const allServerRegexes = await TavernAPI.getRegexes();
            const regex = allServerRegexes.find((r) => r.id === id);
            if (!regex)
                return;
            regex.find_regex = $editor.find('.wio-edit-find').val().toString();
            regex.replace_string = $editor.find('.wio-edit-replace').val().toString();
            await TavernAPI.replaceRegexes(allServerRegexes.filter((r) => r.source !== 'card'));
            await TavernAPI.saveSettings();
            // 更新本地状态
            const localRegex = appState.regexes.global.find((r) => r.id === id) ||
                appState.regexes.character.find((r) => r.id === id);
            if (localRegex) {
                localRegex.find_regex = regex.find_regex;
                localRegex.replace_string = regex.replace_string;
            }
            showSuccessTick('正则已保存');
        }
        // 关闭编辑器
        $container.find('.wio-collapsible-content').slideUp(200, () => {
            $container.find('.wio-collapsible-content').empty();
        });
    });
    const handleCancelEditor = errorCatched(async (event) => {
        event.stopPropagation();
        const $container = $(event.currentTarget).closest('.wio-item-container');
        // 关闭编辑器
        $container.find('.wio-collapsible-content').slideUp(200, () => {
            $container.find('.wio-collapsible-content').empty();
        });
    });
    // --- 多选批量操作函数 ---
    const updateSelectionCount = () => {
        const count = appState.selectedItems.size;
        const parentDoc = parentWin.document;
        $(`#wio-selection-count`, parentDoc).text(`已选择: ${count}`);
    };
    const handleSelectAll = errorCatched(async () => {
        const parentDoc = parentWin.document;
        const $visibleItems = $(`#${PANEL_ID} .wio-item-container:visible, #${PANEL_ID} .wio-book-group:visible`, parentDoc);
        $visibleItems.each((_, element) => {
            const $item = $(element);
            const itemKey = getItemKey($item);
            if (itemKey && canSelectItem($item)) {
                appState.selectedItems.add(itemKey);
                $item.addClass('selected');
            }
        });
        updateSelectionCount();
        showSuccessTick('已全选当前页面的所有项目');
    });
    const handleDeselectAll = errorCatched(async () => {
        const parentDoc = parentWin.document;
        appState.selectedItems.clear();
        $(`#${PANEL_ID} .selected`, parentDoc).removeClass('selected');
        updateSelectionCount();
        showSuccessTick('已取消全选');
    });
    const handleInvertSelection = errorCatched(async () => {
        const parentDoc = parentWin.document;
        const $visibleItems = $(`#${PANEL_ID} .wio-item-container:visible, #${PANEL_ID} .wio-book-group:visible`, parentDoc);
        $visibleItems.each((_, element) => {
            const $item = $(element);
            const itemKey = getItemKey($item);
            if (itemKey && canSelectItem($item)) {
                if (appState.selectedItems.has(itemKey)) {
                    appState.selectedItems.delete(itemKey);
                    $item.removeClass('selected');
                }
                else {
                    appState.selectedItems.add(itemKey);
                    $item.addClass('selected');
                }
            }
        });
        updateSelectionCount();
        showSuccessTick('已反选');
    });
    const getItemKey = ($item) => {
        const isGlobalLoreTab = appState.activeTab === 'global-lore';
        const isBookHeader = $item.hasClass('wio-book-group');
        if (isGlobalLoreTab && isBookHeader) {
            const isEditingEntries = $item.hasClass('editing-entries');
            if (!isEditingEntries) {
                const bookName = $item.data('book-name');
                return `book:${bookName}`;
            }
        }
        else if ($item.hasClass('wio-item-container')) {
            const canSelectItem = isGlobalLoreTab ? $item.closest('.wio-book-group').hasClass('editing-entries') : true;
            if (canSelectItem) {
                const itemType = $item.data('type');
                const itemId = $item.data('id');
                if (itemType === 'lore') {
                    const bookName = $item.data('book-name');
                    return `lore:${bookName}:${itemId}`;
                }
                else {
                    return `regex:${itemId}`;
                }
            }
        }
        return null;
    };
    const canSelectItem = ($item) => {
        const isGlobalLoreTab = appState.activeTab === 'global-lore';
        const isBookHeader = $item.hasClass('wio-book-group');
        if (isGlobalLoreTab && isBookHeader) {
            return !$item.hasClass('editing-entries');
        }
        else if ($item.hasClass('wio-item-container')) {
            return isGlobalLoreTab ? $item.closest('.wio-book-group').hasClass('editing-entries') : true;
        }
        return false;
    };
    const handleBatchEnable = errorCatched(async () => {
        const selectedItems = Array.from(appState.selectedItems);
        if (selectedItems.length === 0) {
            await showModal({ type: 'alert', title: '提示', text: '请先选择要启用的项目。' });
            return;
        }
        const confirmed = await showModal({
            type: 'confirm',
            title: '批量启用',
            text: `确定要启用选中的 ${selectedItems.length} 个项目吗？`,
        });
        if (confirmed) {
            let successCount = 0;
            for (const itemKey of selectedItems) {
                try {
                    if (itemKey.startsWith('book:')) {
                        const bookName = itemKey.substring(5);
                        const settings = await TavernAPI.getLorebookSettings();
                        const currentBooks = new Set(settings.selected_global_lorebooks || []);
                        currentBooks.add(bookName);
                        await TavernAPI.setLorebookSettings({ selected_global_lorebooks: Array.from(currentBooks) });
                        const bookState = appState.allLorebooks.find(b => b.name === bookName);
                        if (bookState)
                            bookState.enabled = true;
                    }
                    else if (itemKey.startsWith('lore:')) {
                        const [, bookName, uid] = itemKey.split(':');
                        await TavernAPI.setLorebookEntries(bookName, [{ uid: Number(uid), enabled: true }]);
                        const entry = safeGetLorebookEntries(bookName).find((e) => e.uid === Number(uid));
                        if (entry)
                            entry.enabled = true;
                    }
                    else if (itemKey.startsWith('regex:')) {
                        const id = itemKey.substring(6);
                        const allServerRegexes = await TavernAPI.getRegexes();
                        const regex = allServerRegexes.find((r) => r.id === id);
                        if (regex) {
                            regex.enabled = true;
                            await TavernAPI.replaceRegexes(allServerRegexes.filter((r) => r.source !== 'card'));
                            const localRegex = appState.regexes.global.find((r) => r.id === id) ||
                                appState.regexes.character.find((r) => r.id === id);
                            if (localRegex)
                                localRegex.enabled = true;
                        }
                    }
                    successCount++;
                }
                catch (error) {
                    console.error(`Failed to enable item ${itemKey}:`, error);
                }
            }
            await TavernAPI.saveSettings();
            showSuccessTick(`已启用 ${successCount} 个项目`);
            renderContent();
        }
    });
    const handleBatchDisable = errorCatched(async () => {
        const selectedItems = Array.from(appState.selectedItems);
        if (selectedItems.length === 0) {
            await showModal({ type: 'alert', title: '提示', text: '请先选择要禁用的项目。' });
            return;
        }
        const confirmed = await showModal({
            type: 'confirm',
            title: '批量禁用',
            text: `确定要禁用选中的 ${selectedItems.length} 个项目吗？`,
        });
        if (confirmed) {
            let successCount = 0;
            for (const itemKey of selectedItems) {
                try {
                    if (itemKey.startsWith('book:')) {
                        const bookName = itemKey.substring(5);
                        const settings = await TavernAPI.getLorebookSettings();
                        const currentBooks = new Set(settings.selected_global_lorebooks || []);
                        currentBooks.delete(bookName);
                        await TavernAPI.setLorebookSettings({ selected_global_lorebooks: Array.from(currentBooks) });
                        const bookState = appState.allLorebooks.find(b => b.name === bookName);
                        if (bookState)
                            bookState.enabled = false;
                    }
                    else if (itemKey.startsWith('lore:')) {
                        const [, bookName, uid] = itemKey.split(':');
                        await TavernAPI.setLorebookEntries(bookName, [{ uid: Number(uid), enabled: false }]);
                        const entry = safeGetLorebookEntries(bookName).find((e) => e.uid === Number(uid));
                        if (entry)
                            entry.enabled = false;
                    }
                    else if (itemKey.startsWith('regex:')) {
                        const id = itemKey.substring(6);
                        const allServerRegexes = await TavernAPI.getRegexes();
                        const regex = allServerRegexes.find((r) => r.id === id);
                        if (regex) {
                            regex.enabled = false;
                            await TavernAPI.replaceRegexes(allServerRegexes.filter((r) => r.source !== 'card'));
                            const localRegex = appState.regexes.global.find((r) => r.id === id) ||
                                appState.regexes.character.find((r) => r.id === id);
                            if (localRegex)
                                localRegex.enabled = false;
                        }
                    }
                    successCount++;
                }
                catch (error) {
                    console.error(`Failed to disable item ${itemKey}:`, error);
                }
            }
            await TavernAPI.saveSettings();
            showSuccessTick(`已禁用 ${successCount} 个项目`);
            renderContent();
        }
    });
    const handleBatchDelete = errorCatched(async () => {
        const selectedItems = Array.from(appState.selectedItems);
        if (selectedItems.length === 0) {
            await showModal({ type: 'alert', title: '提示', text: '请先选择要删除的项目。' });
            return;
        }
        const confirmed = await showModal({
            type: 'confirm',
            title: '批量删除',
            text: `确定要删除选中的 ${selectedItems.length} 个项目吗？此操作不可撤销。`,
        });
        if (confirmed) {
            let successCount = 0;
            for (const itemKey of selectedItems) {
                try {
                    if (itemKey.startsWith('book:')) {
                        const bookName = itemKey.substring(5);
                        await TavernAPI.deleteLorebook(bookName);
                        const index = appState.allLorebooks.findIndex(b => b.name === bookName);
                        if (index !== -1)
                            appState.allLorebooks.splice(index, 1);
                        appState.lorebookEntries.delete(bookName);
                    }
                    else if (itemKey.startsWith('lore:')) {
                        const [, bookName, uid] = itemKey.split(':');
                        await TavernAPI.deleteLorebookEntries(bookName, [uid]);
                        const entries = safeGetLorebookEntries(bookName);
                        const index = entries.findIndex((e) => e.uid === Number(uid));
                        if (index !== -1)
                            entries.splice(index, 1);
                    }
                    // 注意：正则表达式通常不支持批量删除，因为它们可能来自角色卡
                    successCount++;
                }
                catch (error) {
                    console.error(`Failed to delete item ${itemKey}:`, error);
                }
            }
            showSuccessTick(`已删除 ${successCount} 个项目`);
            appState.selectedItems.clear();
            renderContent();
        }
    });
    // --- 事件处理器 ---
    const bindEventHandlers = () => {
        const parentDoc = parentWin.document;
        // 扩展菜单按钮点击事件
        $(parentDoc).on('click', `#${BUTTON_ID}`, async () => {
            const $panel = $(`#${PANEL_ID}`, parentDoc);
            if ($panel.is(':visible')) {
                hidePanel();
            }
            else {
                await showPanel();
            }
        });
        // 面板关闭按钮
        $(parentDoc).on('click', `#${PANEL_ID} .wio-panel-close`, () => {
            hidePanel();
        });
        // 刷新按钮
        $(parentDoc).on('click', `#${REFRESH_BTN_ID}`, () => {
            loadAllData();
        });
        // 标签页切换
        $(parentDoc).on('click', `#${PANEL_ID} .wio-tab-btn`, (event) => {
            const $this = $(event.currentTarget);
            const tabId = $this.data('tab');
            $(`#${PANEL_ID} .wio-tab-btn`, parentDoc).removeClass('active');
            $this.addClass('active');
            appState.activeTab = tabId;
            renderContent();
        });
        // 搜索输入
        $(parentDoc).on('input', `#${SEARCH_INPUT_ID}`, () => {
            renderContent();
        });
        // 搜索过滤器
        $(parentDoc).on('change', `#${PANEL_ID} .wio-search-filters input[type="checkbox"]`, () => {
            renderContent();
        });
        // 替换按钮
        $(parentDoc).on('click', '#wio-replace-btn', () => {
            handleReplace();
        });
        // 新建世界书按钮
        $(parentDoc).on('click', `#${CREATE_LOREBOOK_BTN_ID}`, async () => {
            try {
                const bookName = await showModal({
                    type: 'prompt',
                    title: '新建世界书',
                    text: '请输入世界书名称：',
                    placeholder: '世界书名称',
                });
                if (bookName && typeof bookName === 'string') {
                    const progressToast = showProgressToast('正在创建世界书...');
                    await TavernAPI.createLorebook(bookName.trim());
                    progressToast.remove();
                    showSuccessTick(`世界书 "${bookName}" 创建成功`);
                    loadAllData(); // 重新加载数据
                }
            }
            catch (error) {
                console.error('[WorldInfoOptimizer] Error creating lorebook:', error);
            }
        });
        // 全部折叠按钮
        $(parentDoc).on('click', `#${COLLAPSE_ALL_BTN_ID}`, () => {
            $(`#${PANEL_ID} .wio-book-item`, parentDoc).addClass('collapsed');
        });
        // 多选模式切换
        $(parentDoc).on('click', `#${PANEL_ID} .wio-multi-select-toggle`, (event) => {
            appState.multiSelectMode = !appState.multiSelectMode;
            if (!appState.multiSelectMode) {
                appState.selectedItems.clear();
            }
            renderContent();
        });
        // ESC键关闭面板
        $(parentDoc).on('keydown', (e) => {
            if (e.key === 'Escape') {
                const $panel = $(`#${PANEL_ID}`, parentDoc);
                if ($panel.is(':visible')) {
                    hidePanel();
                }
            }
        });
        // 条目和世界书操作按钮事件
        $(parentDoc).on('click', '.wio-item-header, .wio-global-book-header', handleHeaderClick);
        $(parentDoc).on('click', '.wio-item-toggle, .wio-global-toggle', handleToggleState);
        $(parentDoc).on('click', '.wio-rename-btn', handleRename);
        $(parentDoc).on('click', '.wio-rename-save-btn', handleConfirmRename);
        $(parentDoc).on('click', '.wio-rename-cancel-btn', handleCancelRename);
        $(parentDoc).on('keydown', '.wio-rename-input', handleRenameKeydown);
        $(parentDoc).on('click', '.wio-delete-entry-btn', handleDeleteEntry);
        $(parentDoc).on('click', '.wio-delete-book-btn', handleDeleteBook);
        $(parentDoc).on('click', '.wio-rename-book-btn', handleRenameBook);
        $(parentDoc).on('click', '.wio-create-entry-btn', handleCreateEntry);
        $(parentDoc).on('click', '.wio-batch-recursion-btn', handleBatchSetRecursion);
        $(parentDoc).on('click', '.wio-fix-keywords-btn', handleFixKeywords);
        $(parentDoc).on('click', '.wio-edit-entries-btn', handleEditEntriesToggle);
        $(parentDoc).on('click', `#${COLLAPSE_ALL_BTN_ID}`, handleCollapseAll);
        $(parentDoc).on('click', `#${COLLAPSE_CURRENT_BTN_ID}`, handleCollapseCurrent);
        $(parentDoc).on('click', '.wio-save-btn', handleSaveEditor);
        $(parentDoc).on('click', '.wio-cancel-btn', handleCancelEditor);
        // 多选批量操作按钮
        $(parentDoc).on('click', '#wio-select-all-btn', handleSelectAll);
        $(parentDoc).on('click', '#wio-select-none-btn', handleDeselectAll);
        $(parentDoc).on('click', '#wio-select-invert-btn', handleInvertSelection);
        $(parentDoc).on('click', '#wio-batch-enable-btn', handleBatchEnable);
        $(parentDoc).on('click', '#wio-batch-disable-btn', handleBatchDisable);
        $(parentDoc).on('click', '#wio-batch-delete-btn', handleBatchDelete);
        console.log('[WorldInfoOptimizer] Event handlers bound successfully.');
    };
    // --- 初始化脚本 ---
    console.log('[WorldInfoOptimizer] Starting initialization...');
    onReady(main);
})();
//# sourceMappingURL=index_backup.js.map