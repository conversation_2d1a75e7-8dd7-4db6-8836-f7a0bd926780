/**
 * 世界书优化器 UI 模块
 * 包含所有 DOM 操作和 UI 组件创建函数
 */
import { MESSAGES, PANEL_ID, PROGRESS_TOAST_FADE_DURATION, SEARCH_INPUT_ID, SUCCESS_TOAST_DURATION, REFRESH_BTN_ID, COLLAPSE_CURRENT_BTN_ID, COLLAPSE_ALL_BTN_ID, BUTTON_ID, BUTTON_TOOLTIP, BUTTON_ICON_URL, SELECTORS } from './constants';
import { appState, safeGetLorebookEntries } from './state';
import { escapeHtml, escapeHtmlWithNewlines, highlightText, isMatch } from './utils';
// --- 全局变量 ---
let parentWin;
let $;
/**
 * 设置全局依赖
 */
export const setGlobalDependencies = (jquery, parentWindow) => {
    $ = jquery;
    parentWin = parentWindow;
};
// --- 通知和模态框函数 ---
/**
 * 显示成功提示
 */
export const showSuccessTick = (message = MESSAGES.SUCCESS.OPERATION_SUCCESS, duration = SUCCESS_TOAST_DURATION) => {
    const $panel = $(`#${PANEL_ID}`, parentWin.document);
    if ($panel.length === 0)
        return;
    $panel.find('.wio-toast-notification').remove();
    const $toast = $(`
    <div class="wio-toast-notification wio-success-tick">
      <i class="fa-solid fa-check-circle"></i>
      <span>${escapeHtml(message)}</span>
    </div>
  `);
    $panel.append($toast);
    $toast.fadeIn(200);
    setTimeout(() => {
        $toast.fadeOut(200, () => $toast.remove());
    }, duration);
};
/**
 * 显示进度提示框
 */
export const showProgressToast = (initialMessage = MESSAGES.INFO.PROCESSING) => {
    const $panel = $(`#${PANEL_ID}`, parentWin.document);
    if ($panel.length === 0)
        return { update: () => { }, remove: () => { } };
    $panel.find('.wio-progress-toast').remove();
    const $toast = $(`
    <div class="wio-progress-toast">
      <div class="wio-progress-content">
        <i class="fa-solid fa-spinner fa-spin"></i>
        <span class="wio-progress-text">${escapeHtml(initialMessage)}</span>
      </div>
    </div>
  `);
    $panel.append($toast);
    $toast.fadeIn(PROGRESS_TOAST_FADE_DURATION);
    return {
        update: (message) => {
            $toast.find('.wio-progress-text').text(message);
        },
        remove: () => {
            $toast.fadeOut(PROGRESS_TOAST_FADE_DURATION, () => $toast.remove());
        },
    };
};
/**
 * 显示模态框
 */
export const showModal = (options) => {
    return new Promise((resolve, reject) => {
        const { type = 'alert', title = '通知', text = '', placeholder = '', value = '' } = options;
        let buttonsHtml = '';
        if (type === 'alert') {
            buttonsHtml = '<button class="wio-modal-btn wio-modal-ok">确定</button>';
        }
        else if (type === 'confirm') {
            buttonsHtml =
                '<button class="wio-modal-btn wio-modal-cancel">取消</button><button class="wio-modal-btn wio-modal-ok">确认</button>';
        }
        else if (type === 'prompt') {
            buttonsHtml =
                '<button class="wio-modal-btn wio-modal-cancel">取消</button><button class="wio-modal-btn wio-modal-ok">确定</button>';
        }
        const inputHtml = type === 'prompt'
            ? `<input type="text" class="wio-modal-input" placeholder="${escapeHtml(placeholder)}" value="${escapeHtml(value)}">`
            : '';
        const modalHtml = `
      <div class="wio-modal-overlay">
        <div class="wio-modal-content">
          <div class="wio-modal-header">${escapeHtml(title)}</div>
          <div class="wio-modal-body">
            <p>${escapeHtmlWithNewlines(text)}</p>
            ${inputHtml}
          </div>
          <div class="wio-modal-footer">${buttonsHtml}</div>
        </div>
      </div>
    `;
        const $modal = $(modalHtml);
        $(parentWin.document.body).append($modal);
        $modal.fadeIn(PROGRESS_TOAST_FADE_DURATION);
        const $input = $modal.find('.wio-modal-input');
        if (type === 'prompt')
            $input.focus().select();
        const closeModal = (isSuccess, val) => {
            $modal.fadeOut(PROGRESS_TOAST_FADE_DURATION, () => {
                $modal.remove();
                if (isSuccess)
                    resolve(val);
                else
                    reject(new Error('Modal cancelled'));
            });
        };
        $modal.on('click', '.wio-modal-ok', () => {
            const val = type === 'prompt' ? $input.val() : true;
            if (type === 'prompt' && !String(val).trim()) {
                $input.addClass('wio-input-error');
                setTimeout(() => $input.removeClass('wio-input-error'), 500);
                return;
            }
            closeModal(true, val);
        });
        $modal.on('click', '.wio-modal-cancel', () => closeModal(false));
        if (type === 'prompt') {
            $input.on('keydown', (e) => {
                if (e.key === 'Enter')
                    $modal.find('.wio-modal-ok').click();
                else if (e.key === 'Escape')
                    closeModal(false);
            });
        }
        $modal.on('click', '.wio-modal-overlay', (e) => {
            if (e.target === e.currentTarget)
                closeModal(false);
        });
    });
};
// --- 核心UI元素创建函数 ---
/**
 * 创建项目元素
 */
export const createItemElement = (item, type, bookName = '', searchTerm = '') => {
    const isLore = type === 'lore';
    const id = isLore ? item.uid : item.id;
    const name = isLore ? item.comment || '无标题条目' : item.script_name || '未命名正则';
    const fromCard = item.source === 'card';
    let controlsHtml = '';
    if (appState.multiSelectMode) {
        const itemKey = isLore ? `lore:${bookName}:${id}` : `regex:${id}`;
        const isSelected = appState.selectedItems.has(itemKey);
        controlsHtml = `
      <div class="wio-item-controls">
        <button class="wio-btn wio-select-btn ${isSelected ? 'selected' : ''}" 
                data-item-key="${itemKey}" title="选择此项">
          <i class="fa-solid fa-${isSelected ? 'check-square' : 'square'}"></i>
        </button>
      </div>
    `;
    }
    else {
        controlsHtml = `
      <div class="wio-item-controls">
        <button class="wio-btn wio-toggle-btn" title="切换启用状态">
          <i class="fa-solid fa-${item.enabled ? 'toggle-on' : 'toggle-off'}"></i>
        </button>
        <button class="wio-btn wio-rename-btn" title="重命名">
          <i class="fa-solid fa-edit"></i>
        </button>
        <button class="wio-btn wio-delete-btn" title="删除">
          <i class="fa-solid fa-trash"></i>
        </button>
      </div>
    `;
    }
    const statusClass = item.enabled ? 'enabled' : 'disabled';
    const cardBadge = fromCard ? '<span class="wio-card-badge">卡片</span>' : '';
    const highlightedName = highlightText(name, searchTerm);
    return $(`
    <div class="wio-item-container ${statusClass}" data-type="${type}" data-id="${id}" ${bookName ? `data-book-name="${bookName}"` : ''}>
      <div class="wio-item-header">
        <div class="wio-item-info">
          <span class="wio-item-name">${highlightedName}</span>
          ${cardBadge}
        </div>
        ${controlsHtml}
      </div>
      <div class="wio-collapsible-content" style="display: none;">
        <div class="wio-item-details">
          <!-- 详细内容将在展开时动态加载 -->
        </div>
      </div>
    </div>
  `);
};
/**
 * 创建全局世界书元素
 */
export const createGlobalLorebookElement = (book, entriesToShow, searchTerm) => {
    const enabledClass = book.enabled ? 'enabled' : 'disabled';
    const usageInfo = appState.lorebookUsage.get(book.name) || [];
    const usageText = usageInfo.length > 0 ? `被 ${usageInfo.length} 个角色使用` : '未被使用';
    let controlsHtml = '';
    if (appState.multiSelectMode) {
        const itemKey = `book:${book.name}`;
        const isSelected = appState.selectedItems.has(itemKey);
        controlsHtml = `
      <div class="wio-item-controls">
        <button class="wio-btn wio-select-btn ${isSelected ? 'selected' : ''}" 
                data-item-key="${itemKey}" title="选择此世界书">
          <i class="fa-solid fa-${isSelected ? 'check-square' : 'square'}"></i>
        </button>
      </div>
    `;
    }
    else {
        controlsHtml = `
      <div class="wio-item-controls">
        <button class="wio-btn wio-toggle-btn" title="切换启用状态">
          <i class="fa-solid fa-${book.enabled ? 'toggle-on' : 'toggle-off'}"></i>
        </button>
        <button class="wio-btn wio-create-entry-btn" data-book-name="${book.name}" title="创建条目">
          <i class="fa-solid fa-plus"></i>
        </button>
        <button class="wio-btn wio-rename-btn" title="重命名世界书">
          <i class="fa-solid fa-edit"></i>
        </button>
        <button class="wio-btn wio-delete-btn" title="删除世界书">
          <i class="fa-solid fa-trash"></i>
        </button>
      </div>
    `;
    }
    const highlightedName = highlightText(book.name, searchTerm);
    const entryCount = safeGetLorebookEntries(book.name).length;
    const visibleCount = entriesToShow.length;
    const $bookElement = $(`
    <div class="wio-book-group ${enabledClass}" data-book-name="${book.name}">
      <div class="wio-item-header">
        <div class="wio-item-info">
          <span class="wio-item-name">${highlightedName}</span>
          <span class="wio-item-meta">${entryCount} 条目 | ${usageText}</span>
          ${searchTerm && visibleCount !== entryCount ? `<span class="wio-search-meta">显示 ${visibleCount} 个匹配</span>` : ''}
        </div>
        ${controlsHtml}
      </div>
      <div class="wio-collapsible-content" style="display: none;">
        <div class="wio-entries-container">
          <!-- 条目将在这里动态添加 -->
        </div>
      </div>
    </div>
  `);
    // 添加条目
    const $entriesContainer = $bookElement.find('.wio-entries-container');
    entriesToShow.forEach(entry => {
        const $entryElement = createItemElement(entry, 'lore', book.name, searchTerm);
        $entriesContainer.append($entryElement);
    });
    return $bookElement;
};
// --- 面板显示/隐藏函数 ---
/**
 * 隐藏面板
 */
export const hidePanel = () => {
    const parentDoc = parentWin.document;
    const $panel = $(`#${PANEL_ID}`, parentDoc);
    if ($panel.length > 0) {
        $panel.fadeOut(300);
    }
};
/**
 * 显示面板
 */
export const showPanel = async (loadDataCallback) => {
    const parentDoc = parentWin.document;
    const $panel = $(`#${PANEL_ID}`, parentDoc);
    if ($panel.length > 0) {
        $panel.fadeIn(300);
        // 如果数据未加载，触发加载
        if (!appState.isDataLoaded && loadDataCallback) {
            await loadDataCallback();
        }
    }
};
// --- 渲染函数 ---
/**
 * 渲染主要内容
 */
export const renderContent = () => {
    // 根据全局状态同步所有多选按钮的UI
    const $multiSelectToggles = $(`.wio-multi-select-toggle`, parentWin.document);
    if (appState.multiSelectMode) {
        $multiSelectToggles.addClass('active').html('<i class="fa-solid fa-times"></i> 退出多选');
    }
    else {
        $multiSelectToggles.removeClass('active').html('<i class="fa-solid fa-check-square"></i> 多选模式');
    }
    // 更新选中项目计数
    updateSelectionCount();
    const searchTerm = $(`#${SEARCH_INPUT_ID}`, parentWin.document).val() || '';
    const $container = $(`#${PANEL_ID}-content`, parentWin.document);
    // 根据当前活动标签页渲染不同内容
    switch (appState.activeTab) {
        case 'global-lore':
            renderGlobalLorebookView(searchTerm, $container);
            break;
        case 'character-lore':
            renderCharacterLorebookView(searchTerm, $container);
            break;
        case 'chat-lore':
            renderChatLorebookView(searchTerm, $container);
            break;
        case 'global-regex':
            renderRegexView(appState.regexes.global, searchTerm, $container, '全局正则');
            break;
        case 'character-regex':
            renderRegexView(appState.regexes.character, searchTerm, $container, '角色正则');
            break;
        default:
            $container.html('<p class="wio-info-text">未知的标签页</p>');
    }
};
/**
 * 渲染全局世界书视图
 */
export const renderGlobalLorebookView = (searchTerm, $container) => {
    const books = [...appState.allLorebooks].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.name.localeCompare(b.name));
    if (books.length === 0) {
        $container.html('<p class="wio-info-text">没有找到全局世界书。</p>');
        return;
    }
    const $content = $('<div class="wio-books-container"></div>');
    books.forEach(book => {
        const entries = safeGetLorebookEntries(book.name);
        let entriesToShow = entries;
        if (searchTerm) {
            entriesToShow = entries.filter(entry => isMatch(entry.comment || '', searchTerm) ||
                isMatch(entry.content || '', searchTerm) ||
                (entry.keys && entry.keys.some((key) => isMatch(key, searchTerm))) ||
                isMatch(book.name, searchTerm));
        }
        // 只显示有匹配条目的世界书，或者世界书名称匹配的情况
        if (!searchTerm || entriesToShow.length > 0 || isMatch(book.name, searchTerm)) {
            const $bookElement = createGlobalLorebookElement(book, entriesToShow, searchTerm);
            $content.append($bookElement);
        }
    });
    if ($content.children().length === 0) {
        $container.html(`<p class="wio-info-text">没有找到匹配 "${escapeHtml(searchTerm)}" 的结果。</p>`);
    }
    else {
        $container.html($content);
    }
};
/**
 * 渲染角色世界书视图
 */
export const renderCharacterLorebookView = (searchTerm, $container) => {
    const linkedBooks = appState.lorebooks.character;
    const context = parentWin.SillyTavern.getContext();
    const currentCharName = context?.name || '未知角色';
    if (linkedBooks.length === 0) {
        $container.html(`
      <div class="wio-info-container">
        <p class="wio-info-text">当前角色 "${escapeHtml(currentCharName)}" 没有关联的世界书。</p>
        <p class="wio-info-subtext">您可以在角色设置中为角色关联世界书。</p>
      </div>
    `);
        return;
    }
    const $content = $('<div class="wio-books-container"></div>');
    linkedBooks.forEach(bookName => {
        const book = { name: bookName, enabled: true }; // 角色世界书默认启用
        const entries = safeGetLorebookEntries(bookName);
        let entriesToShow = entries;
        if (searchTerm) {
            entriesToShow = entries.filter(entry => isMatch(entry.comment || '', searchTerm) ||
                isMatch(entry.content || '', searchTerm) ||
                (entry.keys && entry.keys.some((key) => isMatch(key, searchTerm))) ||
                isMatch(bookName, searchTerm));
        }
        if (!searchTerm || entriesToShow.length > 0 || isMatch(bookName, searchTerm)) {
            const $bookElement = createGlobalLorebookElement(book, entriesToShow, searchTerm);
            $content.append($bookElement);
        }
    });
    if ($content.children().length === 0) {
        $container.html(`<p class="wio-info-text">没有找到匹配 "${escapeHtml(searchTerm)}" 的结果。</p>`);
    }
    else {
        const headerHtml = `
      <div class="wio-section-header">
        <h3>角色 "${escapeHtml(currentCharName)}" 的世界书</h3>
      </div>
    `;
        $container.html(headerHtml + $content.prop('outerHTML'));
    }
};
/**
 * 渲染聊天世界书视图
 */
export const renderChatLorebookView = (searchTerm, $container) => {
    const bookName = appState.chatLorebook;
    const context = parentWin.SillyTavern.getContext();
    const currentChatName = context?.chat_metadata?.name || '当前聊天';
    if (!bookName) {
        $container.html(`
      <div class="wio-info-container">
        <p class="wio-info-text">"${escapeHtml(currentChatName)}" 没有关联的聊天世界书。</p>
        <p class="wio-info-subtext">您可以在聊天设置中为当前聊天创建或关联世界书。</p>
      </div>
    `);
        return;
    }
    const entries = safeGetLorebookEntries(bookName);
    let entriesToShow = entries;
    if (searchTerm) {
        entriesToShow = entries.filter(entry => isMatch(entry.comment || '', searchTerm) ||
            isMatch(entry.content || '', searchTerm) ||
            (entry.keys && entry.keys.some((key) => isMatch(key, searchTerm))));
    }
    if (entriesToShow.length === 0 && searchTerm) {
        $container.html(`<p class="wio-info-text">没有找到匹配 "${escapeHtml(searchTerm)}" 的结果。</p>`);
        return;
    }
    const book = { name: bookName, enabled: true };
    const $bookElement = createGlobalLorebookElement(book, entriesToShow, searchTerm);
    const headerHtml = `
    <div class="wio-section-header">
      <h3>聊天 "${escapeHtml(currentChatName)}" 的世界书</h3>
    </div>
  `;
    $container.html(headerHtml + $bookElement.prop('outerHTML'));
};
/**
 * 渲染正则表达式视图
 */
export const renderRegexView = (regexes, searchTerm, $container, title) => {
    if (regexes.length === 0) {
        $container.html(`<p class="wio-info-text">没有找到${title}。</p>`);
        return;
    }
    let filteredRegexes = regexes;
    if (searchTerm) {
        filteredRegexes = regexes.filter(regex => isMatch(regex.script_name || '', searchTerm) ||
            isMatch(regex.find_regex || '', searchTerm) ||
            isMatch(regex.replace_string || '', searchTerm));
    }
    if (filteredRegexes.length === 0) {
        $container.html(`<p class="wio-info-text">没有找到匹配 "${escapeHtml(searchTerm)}" 的${title}。</p>`);
        return;
    }
    const $content = $('<div class="wio-regexes-container"></div>');
    filteredRegexes.forEach(regex => {
        const $regexElement = createItemElement(regex, 'regex', '', searchTerm);
        $content.append($regexElement);
    });
    const headerHtml = `
    <div class="wio-section-header">
      <h3>${title} (${filteredRegexes.length})</h3>
    </div>
  `;
    $container.html(headerHtml + $content.prop('outerHTML'));
};
/**
 * 更新选中项目计数
 */
export const updateSelectionCount = () => {
    const count = appState.selectedItems.size;
    const parentDoc = parentWin.document;
    const $countDisplay = $('.wio-selection-count', parentDoc);
    if (count > 0) {
        $countDisplay.text(`已选择 ${count} 项`).show();
    }
    else {
        $countDisplay.hide();
    }
};
// --- UI 创建函数 ---
/**
 * 创建主面板
 */
export const createMainPanel = () => {
    const parentDoc = parentWin.document;
    // 检查面板是否已存在
    if ($(`#${PANEL_ID}`, parentDoc).length > 0) {
        console.log('[WorldInfoOptimizer] Panel already exists, skipping creation.');
        return;
    }
    const panelHtml = `
    <div id="${PANEL_ID}" class="wio-panel" style="display: none;">
      <div class="wio-panel-header">
        <div class="wio-panel-title">
          <i class="fa-solid fa-book"></i>
          世界书优化器
        </div>
        <div class="wio-panel-controls">
          <button id="wio-minimize-btn" class="wio-btn wio-btn-icon" title="最小化">
            <i class="fa-solid fa-minus"></i>
          </button>
          <button id="wio-close-btn" class="wio-btn wio-btn-icon" title="关闭">
            <i class="fa-solid fa-times"></i>
          </button>
        </div>
      </div>

      <div class="wio-panel-toolbar">
        <div class="wio-search-container">
          <input type="text" id="${SEARCH_INPUT_ID}" class="wio-input" placeholder="搜索世界书、条目、关键词或内容...">
          <button id="wio-clear-search-btn" class="wio-btn wio-btn-icon" title="清空搜索">
            <i class="fa-solid fa-times"></i>
          </button>
        </div>

        <div class="wio-toolbar-actions">
          <button id="${REFRESH_BTN_ID}" class="wio-btn wio-btn-primary" title="刷新数据">
            <i class="fa-solid fa-refresh"></i>
          </button>
          <button class="wio-btn wio-multi-select-toggle" title="多选模式">
            <i class="fa-solid fa-check-square"></i> 多选模式
          </button>
          <button id="${COLLAPSE_CURRENT_BTN_ID}" class="wio-btn" title="折叠当前标签页">
            <i class="fa-solid fa-compress"></i>
          </button>
          <button id="${COLLAPSE_ALL_BTN_ID}" class="wio-btn" title="折叠所有">
            <i class="fa-solid fa-compress-alt"></i>
          </button>
        </div>
      </div>

      <div class="wio-panel-tabs">
        <button class="wio-tab-btn active" data-tab="global-lore">
          <i class="fa-solid fa-globe"></i> 全局世界书
        </button>
        <button class="wio-tab-btn" data-tab="character-lore">
          <i class="fa-solid fa-user"></i> 角色世界书
        </button>
        <button class="wio-tab-btn" data-tab="chat-lore">
          <i class="fa-solid fa-comments"></i> 聊天世界书
        </button>
        <button class="wio-tab-btn" data-tab="global-regex">
          <i class="fa-solid fa-code"></i> 全局正则
        </button>
        <button class="wio-tab-btn" data-tab="character-regex">
          <i class="fa-solid fa-user-code"></i> 角色正则
        </button>
      </div>

      <div class="wio-multi-select-toolbar" style="display: none;">
        <div class="wio-selection-info">
          <span class="wio-selection-count">已选择 0 项</span>
        </div>
        <div class="wio-batch-actions">
          <button class="wio-btn wio-btn-secondary" id="wio-select-all-btn">
            <i class="fa-solid fa-check-double"></i> 全选
          </button>
          <button class="wio-btn wio-btn-secondary" id="wio-deselect-all-btn">
            <i class="fa-solid fa-square"></i> 取消全选
          </button>
          <button class="wio-btn wio-btn-secondary" id="wio-invert-selection-btn">
            <i class="fa-solid fa-exchange-alt"></i> 反选
          </button>
          <button class="wio-btn wio-btn-success" id="wio-batch-enable-btn">
            <i class="fa-solid fa-toggle-on"></i> 批量启用
          </button>
          <button class="wio-btn wio-btn-warning" id="wio-batch-disable-btn">
            <i class="fa-solid fa-toggle-off"></i> 批量禁用
          </button>
          <button class="wio-btn wio-btn-danger" id="wio-batch-delete-btn">
            <i class="fa-solid fa-trash"></i> 批量删除
          </button>
        </div>
      </div>

      <div class="wio-replace-toolbar" style="display: none;">
        <div class="wio-replace-container">
          <input type="text" id="wio-replace-input" class="wio-input" placeholder="替换为...">
          <button id="wio-replace-btn" class="wio-btn wio-btn-primary">
            <i class="fa-solid fa-exchange-alt"></i> 替换
          </button>
          <button id="wio-cancel-replace-btn" class="wio-btn wio-btn-secondary">
            <i class="fa-solid fa-times"></i> 取消
          </button>
        </div>
        <div class="wio-replace-info">
          <small>将在搜索结果中执行替换操作</small>
        </div>
      </div>

      <div id="${PANEL_ID}-content" class="wio-panel-content">
        <div class="wio-loading-placeholder">
          <i class="fa-solid fa-spinner fa-spin"></i>
          <p>正在初始化...</p>
        </div>
      </div>
    </div>
  `;
    $(parentDoc.body).append(panelHtml);
    console.log('[WorldInfoOptimizer] Main panel created successfully.');
};
/**
 * 创建扩展按钮
 */
export const createExtensionButton = () => {
    const parentDoc = parentWin.document;
    // 检查按钮是否已存在
    if ($(`#${BUTTON_ID}`, parentDoc).length > 0) {
        console.log('[WorldInfoOptimizer] Extension button already exists, skipping creation.');
        return;
    }
    const buttonHtml = `
    <div id="${BUTTON_ID}" class="wio-extension-button" title="${BUTTON_TOOLTIP}">
      <img src="${BUTTON_ICON_URL}" alt="世界书优化器" class="wio-button-icon">
      <span class="wio-button-text">世界书优化器</span>
    </div>
  `;
    // 尝试添加到扩展菜单
    const $extensionsMenu = $(SELECTORS.EXTENSIONS_MENU, parentDoc);
    if ($extensionsMenu.length > 0) {
        $extensionsMenu.append(buttonHtml);
        console.log('[WorldInfoOptimizer] Extension button added to extensions menu.');
    }
    else {
        // 如果扩展菜单不存在，添加到页面底部
        $(parentDoc.body).append(`<div class="wio-floating-button">${buttonHtml}</div>`);
        console.log('[WorldInfoOptimizer] Extension button added as floating button.');
    }
};
//# sourceMappingURL=ui.js.map