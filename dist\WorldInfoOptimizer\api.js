/**
 * 世界书优化器 API 交互模块
 * 包含所有与 SillyTavern API 交互的函数
 */
import { PANEL_ID, REFRESH_BTN_ID } from './constants';
import { errorCatched } from './utils';
import { appState, safeSetLorebookEntries, safeClearLorebookEntries, setDataLoaded } from './state';
// --- 全局变量 ---
let TavernHelper;
let parentWin;
let $;
// --- API 包装器 ---
export let TavernAPI = null;
/**
 * 初始化 TavernAPI
 */
export const initializeTavernAPI = (tavernHelper) => {
    TavernHelper = tavernHelper;
    TavernAPI = {
        createLorebook: errorCatched(async (name) => await TavernHelper.createLorebook(name)),
        deleteLorebook: errorCatched(async (name) => await TavernHelper.deleteLorebook(name)),
        getLorebooks: errorCatched(async () => await TavernHelper.getLorebooks()),
        setLorebookSettings: errorCatched(async (settings) => await TavernHelper.setLorebookSettings(settings)),
        getCharData: errorCatched(async () => await TavernHelper.getCharData()),
        Character: TavernHelper.Character || null,
        getRegexes: errorCatched(async () => await TavernHelper.getTavernRegexes({ scope: 'all' })),
        replaceRegexes: errorCatched(async (regexes) => await TavernHelper.replaceTavernRegexes(regexes, { scope: 'all' })),
        getLorebookSettings: errorCatched(async () => await TavernHelper.getLorebookSettings()),
        getCharLorebooks: errorCatched(async (charData) => await TavernHelper.getCharLorebooks(charData)),
        getCurrentCharLorebooks: errorCatched(async () => await TavernHelper.getCharLorebooks()),
        getChatLorebook: errorCatched(async () => await TavernHelper.getChatLorebook()),
        getOrCreateChatLorebook: errorCatched(async (name) => await TavernHelper.getOrCreateChatLorebook(name)),
        setChatLorebook: errorCatched(async (name) => await TavernHelper.setChatLorebook(name)),
        getLorebookEntries: errorCatched(async (name) => await TavernHelper.getLorebookEntries(name)),
        setLorebookEntries: errorCatched(async (name, entries) => await TavernHelper.setLorebookEntries(name, entries)),
        createLorebookEntries: errorCatched(async (name, entries) => await TavernHelper.createLorebookEntries(name, entries)),
        deleteLorebookEntries: errorCatched(async (name, uids) => await TavernHelper.deleteLorebookEntries(name, uids)),
        saveSettings: errorCatched(async () => await TavernHelper.builtin.saveSettings()),
        setCurrentCharLorebooks: errorCatched(async (lorebooks) => await TavernHelper.setCurrentCharLorebooks(lorebooks)),
    };
};
/**
 * 设置全局依赖
 */
export const setGlobalDependencies = (jquery, parentWindow) => {
    $ = jquery;
    parentWin = parentWindow;
};
/**
 * 更新角色正则表达式
 */
export const updateCharacterRegexes = (allUIRegexes, charData) => {
    const characterUIRegexes = allUIRegexes?.filter((r) => r.scope === 'character') || [];
    let cardRegexes = [];
    if (charData?.data?.extensions?.regex_scripts) {
        cardRegexes = Array.isArray(charData.data.extensions.regex_scripts)
            ? charData.data.extensions.regex_scripts
            : [];
    }
    // 合并角色正则和卡片正则
    appState.regexes.character = [...characterUIRegexes, ...cardRegexes];
};
/**
 * 更新角色世界书
 */
export const updateCharacterLorebooks = (charBooks) => {
    const characterBookNames = [];
    if (charBooks) {
        if (charBooks.primary && typeof charBooks.primary === 'string') {
            characterBookNames.push(charBooks.primary);
        }
        if (Array.isArray(charBooks.additional)) {
            charBooks.additional.forEach((name) => {
                if (typeof name === 'string') {
                    characterBookNames.push(name);
                }
            });
        }
    }
    appState.lorebooks.character = characterBookNames;
};
/**
 * 获取过滤后的世界书数据
 */
export const getFilteredLorebookData = (books, searchTerm) => {
    if (!searchTerm) {
        return books.map(book => ({
            ...book,
            entries: [],
            filteredEntries: [],
        }));
    }
    // 这里需要导入 isMatch 函数，但为了避免循环依赖，暂时使用简单的包含检查
    const simpleMatch = (text, term) => text && text.toLowerCase().includes(term.toLowerCase());
    return books.map(book => {
        const entries = appState.lorebookEntries.get(book.name) || [];
        const filteredEntries = entries.filter(entry => simpleMatch(entry.comment, searchTerm) ||
            simpleMatch(entry.content, searchTerm) ||
            (entry.keys && entry.keys.some((key) => simpleMatch(key, searchTerm))));
        return {
            ...book,
            entries,
            filteredEntries,
        };
    });
};
// --- 数据加载函数 ---
/**
 * 加载所有数据
 */
export const loadAllData = errorCatched(async (renderContent) => {
    const $content = $(`#${PANEL_ID}-content`, parentWin.document);
    $content.html(`
    <div class="wio-loading-container">
      <div class="wio-loading-title">数据同步中...</div>
      <div class="wio-loading-progress-bar-container">
        <div id="wio-loading-bar" class="wio-loading-progress-bar" style="width: 0%;"></div>
      </div>
      <div id="wio-loading-status" class="wio-loading-status-text">正在初始化...</div>
    </div>
  `);
    const $progressBar = $('#wio-loading-bar', parentWin.document);
    const $statusText = $('#wio-loading-status', parentWin.document);
    const updateProgress = (percentage, text) => {
        if ($progressBar.length)
            $progressBar.css('width', `${Math.min(100, Math.max(0, percentage))}%`);
        if ($statusText.length)
            $statusText.text(text);
    };
    try {
        updateProgress(5, '正在连接 SillyTavern API...');
        // 防御性检查：确保SillyTavern API可用
        if (!parentWin.SillyTavern || !parentWin.SillyTavern.getContext) {
            console.warn('[WorldInfoOptimizer] SillyTavern API not available, initializing with empty data');
            appState.regexes.global = [];
            appState.regexes.character = [];
            appState.allLorebooks = [];
            appState.lorebooks.character = [];
            appState.chatLorebook = null;
            safeClearLorebookEntries();
            setDataLoaded(true);
            renderContent();
            return;
        }
        const context = parentWin.SillyTavern.getContext() || {};
        const allCharacters = Array.isArray(context.characters) ? context.characters : [];
        const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;
        const hasActiveChat = context.chatId !== undefined && context.chatId !== null;
        let charData = null, charLinkedBooks = null, chatLorebook = null;
        updateProgress(10, '正在获取核心设置...');
        // 使用Promise.allSettled来避免单个失败影响整体
        const promises = [
            TavernAPI.getRegexes().catch(() => []),
            TavernAPI.getLorebookSettings().catch(() => ({})),
            TavernAPI.getLorebooks().catch(() => []),
        ];
        if (hasActiveCharacter) {
            promises.push(TavernAPI.getCharData().catch(() => null));
            promises.push(TavernAPI.getCurrentCharLorebooks().catch(() => null));
        }
        else {
            promises.push(Promise.resolve(null), Promise.resolve(null));
        }
        if (hasActiveChat) {
            promises.push(TavernAPI.getChatLorebook().catch((error) => {
                console.warn('[WorldInfoOptimizer] Failed to get chat lorebook:', error);
                return null;
            }));
        }
        else {
            promises.push(Promise.resolve(null));
        }
        const results = await Promise.allSettled(promises);
        updateProgress(20, '核心数据已获取，正在分析...');
        // 安全提取结果
        const allUIRegexes = results[0].status === 'fulfilled' ? results[0].value : [];
        const globalSettings = results[1].status === 'fulfilled' ? results[1].value : {};
        const allBookFileNames = results[2].status === 'fulfilled' ? results[2].value : [];
        charData = results[3]?.status === 'fulfilled' ? results[3].value : null;
        charLinkedBooks = results[4]?.status === 'fulfilled' ? results[4].value : null;
        chatLorebook = results[5]?.status === 'fulfilled' ? results[5].value : null;
        appState.regexes.global = Array.isArray(allUIRegexes)
            ? allUIRegexes.filter((r) => r.scope === 'global')
            : [];
        updateCharacterRegexes(allUIRegexes, charData);
        safeClearLorebookEntries();
        appState.lorebookUsage.clear();
        const knownBookNames = new Set(Array.isArray(allBookFileNames) ? allBookFileNames : []);
        updateProgress(30, '正在分析角色数据...');
        // 安全处理角色世界书
        if (Array.isArray(allCharacters) && allCharacters.length > 0) {
            await processCharacterLorebooks(allCharacters, knownBookNames, updateProgress);
        }
        updateProgress(50, '角色数据分析完毕，正在整理世界书列表...');
        const enabledGlobalBooks = new Set(Array.isArray(globalSettings?.selected_global_lorebooks) ? globalSettings.selected_global_lorebooks : []);
        appState.allLorebooks = (Array.isArray(allBookFileNames) ? allBookFileNames : []).map((name) => ({
            name: name,
            enabled: enabledGlobalBooks.has(name),
        }));
        const charBookSet = new Set();
        if (charLinkedBooks && typeof charLinkedBooks === 'object') {
            if (charLinkedBooks.primary && typeof charLinkedBooks.primary === 'string') {
                charBookSet.add(charLinkedBooks.primary);
            }
            if (Array.isArray(charLinkedBooks.additional)) {
                charLinkedBooks.additional.forEach((name) => typeof name === 'string' && charBookSet.add(name));
            }
        }
        appState.lorebooks.character = Array.from(charBookSet);
        appState.chatLorebook = typeof chatLorebook === 'string' ? chatLorebook : null;
        if (typeof chatLorebook === 'string') {
            knownBookNames.add(chatLorebook);
        }
        await loadLorebookEntries(knownBookNames, allBookFileNames, updateProgress);
        updateProgress(100, '加载完成，正在渲染界面...');
        setDataLoaded(true);
        renderContent();
    }
    catch (error) {
        console.error('[WorldInfoOptimizer] Error in loadAllData:', error);
        $content.html(`
      <div style="padding: 20px; text-align: center;">
        <p style="color: #ff6b6b; margin-bottom: 10px;">
          <i class="fa-solid fa-exclamation-triangle"></i> 数据加载失败
        </p>
        <p style="color: #666; font-size: 14px;">
          请检查开发者控制台获取详细信息，或尝试刷新页面。
        </p>
        <button class="wio-modal-btn" onclick="$('#${REFRESH_BTN_ID}').click()"
                style="margin-top: 15px; padding: 8px 16px;">
          <i class="fa-solid fa-refresh"></i> 重试
        </button>
      </div>
    `);
        throw error;
    }
});
/**
 * 处理角色世界书
 */
const processCharacterLorebooks = async (allCharacters, knownBookNames, updateProgress) => {
    try {
        for (let i = 0; i < allCharacters.length; i++) {
            const char = allCharacters[i];
            if (!char || !char.name)
                continue;
            try {
                let books = null;
                try {
                    const result = TavernHelper.getCharLorebooks({ name: char.name });
                    books = result && typeof result.then === 'function' ? await result : result;
                }
                catch (error) {
                    console.warn(`[WorldInfoOptimizer] Error getting lorebooks for character "${char.name}":`, error);
                }
                if (books && typeof books === 'object') {
                    const bookSet = new Set();
                    if (books.primary && typeof books.primary === 'string')
                        bookSet.add(books.primary);
                    if (Array.isArray(books.additional)) {
                        books.additional.forEach((b) => typeof b === 'string' && bookSet.add(b));
                    }
                    bookSet.forEach(bookName => {
                        if (typeof bookName === 'string') {
                            if (!appState.lorebookUsage.has(bookName)) {
                                appState.lorebookUsage.set(bookName, []);
                            }
                            appState.lorebookUsage.get(bookName).push(char.name);
                            knownBookNames.add(bookName);
                        }
                    });
                }
            }
            catch (charError) {
                console.warn(`[WorldInfoOptimizer] Error processing character ${char.name}:`, charError);
            }
            const charProgress = 30 + (i / allCharacters.length) * 20; // This stage takes 20%
            updateProgress(charProgress, `正在分析角色: ${char.name}`);
        }
    }
    catch (charProcessingError) {
        console.warn('[WorldInfoOptimizer] Error processing characters:', charProcessingError);
    }
};
/**
 * 加载世界书条目
 */
const loadLorebookEntries = async (knownBookNames, allBookFileNames, updateProgress) => {
    const allBooksToLoad = Array.from(knownBookNames);
    const existingBookFiles = new Set(Array.isArray(allBookFileNames) ? allBookFileNames : []);
    updateProgress(55, `准备加载 ${allBooksToLoad.length} 个世界书的条目...`);
    // 分批加载世界书条目
    const batchSize = 5;
    for (let i = 0; i < allBooksToLoad.length; i += batchSize) {
        const batch = allBooksToLoad.slice(i, i + batchSize);
        await Promise.allSettled(batch.map(async (name) => {
            if (existingBookFiles.has(name) && typeof name === 'string') {
                try {
                    const entries = await TavernAPI.getLorebookEntries(name).catch(() => []);
                    safeSetLorebookEntries(name, entries);
                }
                catch (entryError) {
                    console.warn(`[WorldInfoOptimizer] Error loading entries for book ${name}:`, entryError);
                }
            }
        }));
        const bookProgress = 55 + ((i + batch.length) / allBooksToLoad.length) * 40; // This stage takes 40%
        updateProgress(bookProgress, `已加载 ${i + batch.length} / ${allBooksToLoad.length} 个世界书`);
    }
};
//# sourceMappingURL=api.js.map