/**
 * 世界书优化器常量定义
 * 包含所有配置常量、ID常量和选项配置
 */
// --- 脚本基础配置 ---
export const SCRIPT_VERSION_TAG = 'v1_0_0';
// --- UI 元素 ID 常量 ---
export const PANEL_ID = 'world-info-optimizer-panel';
export const BUTTON_ID = 'world-info-optimizer-button';
export const SEARCH_INPUT_ID = 'wio-search-input';
export const REFRESH_BTN_ID = 'wio-refresh-btn';
export const COLLAPSE_CURRENT_BTN_ID = 'wio-collapse-current-btn';
export const COLLAPSE_ALL_BTN_ID = 'wio-collapse-all-btn';
export const CREATE_LOREBOOK_BTN_ID = 'wio-create-lorebook-btn';
// --- 按钮配置 ---
export const BUTTON_ICON_URL = 'https://i.postimg.cc/bY23wb9Y/IMG-20250626-000247.png';
export const BUTTON_TOOLTIP = '世界书优化器';
export const BUTTON_TEXT_IN_MENU = '世界书优化器';
// --- 世界书选项配置 ---
export const LOREBOOK_OPTIONS = {
    position: {
        before_character_definition: '角色定义前',
        after_character_definition: '角色定义后',
        before_example_messages: '聊天示例前',
        after_example_messages: '聊天示例后',
        before_author_note: '作者笔记前',
        after_author_note: '作者笔记后',
        at_depth_as_system: '@D ⚙ 系统',
        at_depth_as_assistant: '@D 🗨️ 角色',
        at_depth_as_user: '@D 👤 用户',
    },
    logic: {
        and_any: '任一 AND',
        and_all: '所有 AND',
        not_any: '任一 NOT',
        not_all: '所有 NOT',
    },
};
// --- 默认配置 ---
export const DEFAULT_SEARCH_FILTERS = {
    bookName: true,
    entryName: true,
    keywords: true,
    content: true,
};
export const DEFAULT_TAB = 'global-lore';
// --- 操作配置 ---
export const MAX_RETRIES = 100;
export const SUCCESS_TOAST_DURATION = 1500;
export const PROGRESS_TOAST_FADE_DURATION = 200;
export const DEBOUNCE_DELAY = 300;
// --- CSS 类名常量 ---
export const CSS_CLASSES = {
    // 面板相关
    PANEL: 'wio-panel',
    PANEL_CONTENT: 'wio-panel-content',
    PANEL_HEADER: 'wio-panel-header',
    // 项目相关
    ITEM_CONTAINER: 'wio-item-container',
    ITEM_HEADER: 'wio-item-header',
    ITEM_NAME: 'wio-item-name',
    BOOK_GROUP: 'wio-book-group',
    // 按钮相关
    BUTTON: 'wio-btn',
    BUTTON_PRIMARY: 'wio-btn-primary',
    BUTTON_SECONDARY: 'wio-btn-secondary',
    BUTTON_DANGER: 'wio-btn-danger',
    // 输入相关
    INPUT: 'wio-input',
    INPUT_ERROR: 'wio-input-error',
    // 状态相关
    ENABLED: 'wio-enabled',
    DISABLED: 'wio-disabled',
    COLLAPSED: 'wio-collapsed',
    EXPANDED: 'wio-expanded',
    // 多选相关
    MULTI_SELECT_TOGGLE: 'wio-multi-select-toggle',
    SELECTED: 'wio-selected',
    // 模态框相关
    MODAL_OVERLAY: 'wio-modal-overlay',
    MODAL_CONTENT: 'wio-modal-content',
    MODAL_HEADER: 'wio-modal-header',
    MODAL_BODY: 'wio-modal-body',
    MODAL_FOOTER: 'wio-modal-footer',
    MODAL_INPUT: 'wio-modal-input',
    MODAL_BTN: 'wio-modal-btn',
    MODAL_OK: 'wio-modal-ok',
    MODAL_CANCEL: 'wio-modal-cancel',
    // 编辑器相关
    EDITOR_WRAPPER: 'wio-editor-wrapper',
    EDITOR_TEXTAREA: 'wio-editor-textarea',
    // 工具提示
    INFO_TEXT: 'wio-info-text',
    SUCCESS_TICK: 'wio-success-tick',
    PROGRESS_TOAST: 'wio-progress-toast',
    // 可折叠内容
    COLLAPSIBLE_CONTENT: 'wio-collapsible-content',
    // 重命名相关
    RENAME_INPUT: 'wio-rename-input',
    RENAME_SAVE_BTN: 'wio-rename-save-btn',
    RENAME_CANCEL_BTN: 'wio-rename-cancel-btn',
};
// --- 选择器常量 ---
export const SELECTORS = {
    EXTENSIONS_MENU: '#extensionsMenu',
    PANEL: `#${PANEL_ID}`,
    PANEL_CONTENT: `#${PANEL_ID}-content`,
    SEARCH_INPUT: `#${SEARCH_INPUT_ID}`,
    REFRESH_BTN: `#${REFRESH_BTN_ID}`,
    COLLAPSE_CURRENT_BTN: `#${COLLAPSE_CURRENT_BTN_ID}`,
    COLLAPSE_ALL_BTN: `#${COLLAPSE_ALL_BTN_ID}`,
    CREATE_LOREBOOK_BTN: `#${CREATE_LOREBOOK_BTN_ID}`,
};
// --- 消息常量 ---
export const MESSAGES = {
    SUCCESS: {
        OPERATION_SUCCESS: '操作成功',
        ENTRY_CREATED: '条目创建成功',
        BOOK_CREATED: '世界书创建成功',
        RENAME_SUCCESS: '重命名成功',
        DELETE_SUCCESS: '删除成功',
    },
    ERROR: {
        OPERATION_FAILED: '操作失败',
        INVALID_INPUT: '输入无效',
        NETWORK_ERROR: '网络错误',
        PERMISSION_DENIED: '权限不足',
    },
    CONFIRM: {
        DELETE_ENTRY: '确定要删除这个条目吗？',
        DELETE_BOOK: '确定要删除这个世界书吗？这将删除其中的所有条目。',
        BATCH_DELETE: '确定要删除选中的项目吗？',
        REPLACE_ALL: '确定要执行替换操作吗？',
    },
    PROMPT: {
        ENTER_NAME: '请输入名称',
        ENTER_NEW_NAME: '请输入新名称',
        ENTER_REPLACE_TEXT: '请输入替换文本',
    },
    INFO: {
        NO_RESULTS: '没有找到匹配的结果',
        LOADING: '正在加载...',
        PROCESSING: '正在处理...',
        NO_SELECTION: '请先选择要操作的项目',
    },
};
//# sourceMappingURL=constants.js.map