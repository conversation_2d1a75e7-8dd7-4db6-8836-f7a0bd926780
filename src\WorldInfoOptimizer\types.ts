/**
 * 世界书优化器类型定义
 * 包含所有接口和类型定义
 */

/**
 * 世界书条目接口
 */
export interface LorebookEntry {
  uid: string;
  comment: string;
  content: string;
  keys: string[];
  enabled: boolean;
  display_index: number;
  [key: string]: any;
}

/**
 * 应用程序状态接口
 */
export interface AppState {
  regexes: {
    global: any[];
    character: any[];
  };
  lorebooks: {
    character: string[];
  };
  chatLorebook: string | null;
  allLorebooks: Array<{ name: string; enabled: boolean }>;
  lorebookEntries: Map<string, LorebookEntry[]>;
  lorebookUsage: Map<string, string[]>;
  activeTab: string;
  isDataLoaded: boolean;
  searchFilters: {
    bookName: boolean;
    entryName: boolean;
    keywords: boolean;
    content: boolean;
  };
  multiSelectMode: boolean;
  selectedItems: Set<string>;
}

/**
 * 模态框选项接口
 */
export interface ModalOptions {
  type?: 'alert' | 'confirm' | 'prompt';
  title?: string;
  text?: string;
  placeholder?: string;
  value?: string;
}

/**
 * 进度提示框返回接口
 */
export interface ProgressToast {
  update: (message: string) => void;
  remove: () => void;
}

/**
 * 项目类型枚举
 */
export type ItemType = 'lore' | 'regex';

/**
 * 标签页类型
 */
export type TabType = 'global-lore' | 'character-lore' | 'chat-lore' | 'global-regex' | 'character-regex';

/**
 * 世界书位置选项类型
 */
export type LorebookPosition = 
  | 'before_character_definition'
  | 'after_character_definition'
  | 'before_example_messages'
  | 'after_example_messages'
  | 'before_author_note'
  | 'after_author_note'
  | 'at_depth_as_system'
  | 'at_depth_as_assistant'
  | 'at_depth_as_user';

/**
 * 世界书逻辑选项类型
 */
export type LorebookLogic = 'and_any' | 'and_all' | 'not_any' | 'not_all';

/**
 * 错误处理包装器类型
 */
export type ErrorCatchedFunction<T extends (...args: any[]) => any> = T;

/**
 * jQuery 对象类型（简化）
 */
export interface JQueryLike {
  [key: string]: any;
}

/**
 * TavernHelper API 接口
 */
export interface TavernHelperAPI {
  createLorebook: (name: string) => Promise<any>;
  [key: string]: any;
}

/**
 * 匹配结果接口
 */
export interface MatchResult {
  type: ItemType;
  bookName?: string;
  item: LorebookEntry | any;
  field: string;
  matchText: string;
}
